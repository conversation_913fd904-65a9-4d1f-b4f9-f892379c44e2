# 江豚检测系统配置文件 - 性能优化版本

# 模型配置
model:
  weights_path: "best.pt"  # 训练好的模型权重路径
  confidence_threshold: 0.3  # 提高置信度阈值以减少误检
  iou_threshold: 0.5  # 提高IoU阈值以减少重复检测
  device: "auto"  # 设备选择: auto, cpu, cuda, mps

# SAHI切片检测配置 - 性能优化
sahi:
  enabled: false  # 默认关闭SAHI以提高速度
  slice_height: 480  # 更小的切片尺寸
  slice_width: 480   # 更小的切片尺寸
  overlap_height_ratio: 0.05  # 最小重叠比例
  overlap_width_ratio: 0.05   # 最小重叠比例
  postprocess_type: "NMS"    # 后处理类型
  postprocess_match_metric: "IOU"  # 匹配度量
  postprocess_match_threshold: 0.6  # 更高的匹配阈值

# DJI Mavic 3 相机参数
mavic3_camera:
  sensor_width_mm: 17.3  # 传感器宽度 (mm)
  sensor_height_mm: 13.0  # 传感器高度 (mm)
  focal_length_mm: 24.0   # 焦距 (mm)
  image_width_px: 5280    # 图像宽度 (像素)
  image_height_px: 3956   # 图像高度 (像素)

# GPS计算配置
gps:
  altitude_offset: 0.0  # 高度偏移量 (米)
  coordinate_system: "WGS84"  # 坐标系统
  precision_decimal: 6  # GPS坐标精度（小数位数）

# 检测类别配置
classes:
  finless_porpoise: 0  # 江豚类别ID

# 输出配置
output:
  save_annotated_images: true  # 是否保存标注图像
  save_crops: false  # 不保存裁剪图像以节省空间
  csv_delimiter: ","  # CSV分隔符
  image_format: "jpg"  # 输出图像格式
  
# 批处理配置
batch_processing:
  max_workers: 2  # 减少并行处理数以节省内存
  chunk_size: 5  # 减小批次大小

# 视频处理配置
video:
  extract_fps: 0.5  # 降低帧提取率
  supported_formats: [".mp4", ".avi", ".mov", ".mkv"]

# 日志配置
logging:
  level: "WARNING"  # 减少日志输出
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  save_to_file: false  # 不保存日志文件以提高性能

# 可视化配置
visualization:
  bbox_color: [0, 255, 0]  # 边界框颜色 (BGR)
  bbox_thickness: 2  # 边界框线条粗细
  text_color: [255, 255, 255]  # 文本颜色 (BGR)
  text_scale: 0.7  # 文本大小
  show_confidence: true  # 是否显示置信度 