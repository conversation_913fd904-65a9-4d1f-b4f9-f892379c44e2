"""
视频处理模块
从航拍视频中提取帧并进行江豚检测
"""

import cv2
import os
from pathlib import Path
from typing import List, Dict, Optional
import logging
from datetime import datetime, timedelta
import json

try:
    from moviepy.editor import VideoFileClip
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False
    print("警告: moviepy未安装，请运行: pip install moviepy")

from .detector import FinlessPorpoiseDetector


class VideoProcessor:
    """视频处理器类"""
    
    def __init__(self, detector: FinlessPorpoiseDetector, config: Dict = None):
        """
        初始化视频处理器
        
        Args:
            detector: 江豚检测器实例
            config: 配置字典
        """
        self.detector = detector
        self.config = config or detector.config
        self.logger = logging.getLogger('VideoProcessor')
        
        # 视频处理参数
        self.extract_fps = self.config.get('video', {}).get('extract_fps', 1)
        self.supported_formats = self.config.get('video', {}).get(
            'supported_formats', ['.mp4', '.avi', '.mov', '.mkv']
        )
    
    def extract_frames(self, video_path: str, output_dir: str, 
                      fps: float = None, start_time: float = 0, 
                      end_time: float = None) -> List[str]:
        """
        从视频中提取帧
        
        Args:
            video_path: 视频文件路径
            output_dir: 输出目录
            fps: 提取帧率（每秒提取帧数）
            start_time: 开始时间（秒）
            end_time: 结束时间（秒）
            
        Returns:
            提取的帧文件路径列表
        """
        if fps is None:
            fps = self.extract_fps
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        video_name = Path(video_path).stem
        frame_paths = []
        
        try:
            # 使用OpenCV读取视频
            cap = cv2.VideoCapture(video_path)
            
            if not cap.isOpened():
                self.logger.error(f"无法打开视频文件: {video_path}")
                return []
            
            # 获取视频信息
            video_fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / video_fps
            
            self.logger.info(f"视频信息: {video_fps:.2f}fps, {total_frames}帧, {duration:.2f}秒")
            
            # 计算提取间隔
            frame_interval = int(video_fps / fps)
            
            # 设置开始位置
            if start_time > 0:
                start_frame = int(start_time * video_fps)
                cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
            
            # 设置结束位置
            if end_time is not None:
                end_frame = int(end_time * video_fps)
            else:
                end_frame = total_frames
            
            frame_count = 0
            extracted_count = 0
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                current_frame = int(cap.get(cv2.CAP_PROP_POS_FRAMES))
                
                # 检查是否超过结束帧
                if current_frame > end_frame:
                    break
                
                # 按间隔提取帧
                if frame_count % frame_interval == 0:
                    timestamp = current_frame / video_fps
                    frame_filename = f"{video_name}_frame_{extracted_count:06d}_t{timestamp:.2f}s.jpg"
                    frame_path = output_path / frame_filename
                    
                    # 保存帧
                    cv2.imwrite(str(frame_path), frame)
                    frame_paths.append(str(frame_path))
                    extracted_count += 1
                    
                    if extracted_count % 10 == 0:
                        self.logger.info(f"已提取 {extracted_count} 帧")
                
                frame_count += 1
            
            cap.release()
            
            self.logger.info(f"帧提取完成，共提取 {len(frame_paths)} 帧")
            return frame_paths
            
        except Exception as e:
            self.logger.error(f"帧提取失败: {e}")
            return []
    
    def extract_frames_with_moviepy(self, video_path: str, output_dir: str,
                                   fps: float = None) -> List[str]:
        """
        使用MoviePy提取视频帧（更精确的时间控制）
        
        Args:
            video_path: 视频文件路径
            output_dir: 输出目录
            fps: 提取帧率
            
        Returns:
            提取的帧文件路径列表
        """
        if not MOVIEPY_AVAILABLE:
            self.logger.warning("MoviePy未安装，使用OpenCV提取帧")
            return self.extract_frames(video_path, output_dir, fps)
        
        if fps is None:
            fps = self.extract_fps
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        video_name = Path(video_path).stem
        frame_paths = []
        
        try:
            # 加载视频
            clip = VideoFileClip(video_path)
            duration = clip.duration
            
            self.logger.info(f"视频时长: {duration:.2f}秒")
            
            # 计算时间点
            time_points = []
            current_time = 0
            while current_time < duration:
                time_points.append(current_time)
                current_time += 1.0 / fps
            
            # 提取帧
            for i, t in enumerate(time_points):
                try:
                    frame = clip.get_frame(t)
                    frame_filename = f"{video_name}_frame_{i:06d}_t{t:.2f}s.jpg"
                    frame_path = output_path / frame_filename
                    
                    # 转换BGR格式并保存
                    frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                    cv2.imwrite(str(frame_path), frame_bgr)
                    frame_paths.append(str(frame_path))
                    
                    if i % 10 == 0:
                        self.logger.info(f"已提取 {i+1}/{len(time_points)} 帧")
                        
                except Exception as e:
                    self.logger.warning(f"提取第{i}帧失败: {e}")
                    continue
            
            clip.close()
            
            self.logger.info(f"MoviePy帧提取完成，共提取 {len(frame_paths)} 帧")
            return frame_paths
            
        except Exception as e:
            self.logger.error(f"MoviePy帧提取失败: {e}")
            return []
    
    def process_video(self, video_path: str, output_dir: str = None,
                     extract_fps: float = None, use_moviepy: bool = False) -> Dict:
        """
        处理视频文件：提取帧并进行江豚检测
        
        Args:
            video_path: 视频文件路径
            output_dir: 输出目录
            extract_fps: 提取帧率
            use_moviepy: 是否使用MoviePy提取帧
            
        Returns:
            处理结果字典
        """
        video_path = Path(video_path)
        
        if not video_path.exists():
            self.logger.error(f"视频文件不存在: {video_path}")
            return {}
        
        if video_path.suffix.lower() not in self.supported_formats:
            self.logger.error(f"不支持的视频格式: {video_path.suffix}")
            return {}
        
        if output_dir is None:
            output_dir = f"video_detection_{video_path.stem}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"开始处理视频: {video_path}")
        
        # 创建子目录
        frames_dir = output_path / "extracted_frames"
        detections_dir = output_path / "detections"
        
        try:
            # 1. 提取帧
            self.logger.info("第1步: 提取视频帧")
            if use_moviepy:
                frame_paths = self.extract_frames_with_moviepy(
                    str(video_path), str(frames_dir), extract_fps
                )
            else:
                frame_paths = self.extract_frames(
                    str(video_path), str(frames_dir), extract_fps
                )
            
            if not frame_paths:
                self.logger.error("帧提取失败")
                return {}
            
            # 2. 检测江豚
            self.logger.info("第2步: 检测江豚")
            all_detections = []
            
            for i, frame_path in enumerate(frame_paths):
                self.logger.info(f"检测进度: {i+1}/{len(frame_paths)} - {Path(frame_path).name}")
                
                detections = self.detector.detect_image(frame_path)
                
                # 添加视频相关信息
                for detection in detections:
                    detection['source_video'] = str(video_path)
                    detection['frame_number'] = i
                    detection['frame_path'] = frame_path
                    
                    # 从文件名解析时间戳
                    filename = Path(frame_path).stem
                    if '_t' in filename:
                        try:
                            time_part = filename.split('_t')[1].replace('s', '')
                            detection['video_timestamp'] = float(time_part)
                        except:
                            detection['video_timestamp'] = i / (extract_fps or self.extract_fps)
                    else:
                        detection['video_timestamp'] = i / (extract_fps or self.extract_fps)
                
                all_detections.extend(detections)
            
            # 3. 保存结果
            self.logger.info("第3步: 保存检测结果")
            
            # 保存CSV
            if all_detections:
                import pandas as pd
                df = pd.DataFrame(all_detections)
                csv_path = output_path / "video_detection_results.csv"
                df.to_csv(csv_path, index=False, encoding='utf-8-sig')
                
                # 生成视频检测报告
                self._generate_video_report(df, str(video_path), str(output_path))
                
                # 可选：创建检测视频
                if self.config.get('output', {}).get('create_detection_video', False):
                    self._create_detection_video(frame_paths, all_detections, str(output_path))
            
            # 4. 清理临时帧文件（可选）
            if self.config.get('video', {}).get('cleanup_frames', False):
                self._cleanup_frames(frame_paths)
            
            result = {
                'video_path': str(video_path),
                'output_dir': str(output_path),
                'total_frames': len(frame_paths),
                'total_detections': len(all_detections),
                'frames_with_detections': len(set(d['frame_path'] for d in all_detections)),
                'average_confidence': sum(d['confidence'] for d in all_detections) / len(all_detections) if all_detections else 0,
                'processing_time': datetime.now().isoformat()
            }
            
            self.logger.info(f"视频处理完成: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"视频处理失败: {e}")
            return {}
    
    def _generate_video_report(self, df, video_path: str, output_dir: str):
        """生成视频检测报告"""
        try:
            # 按时间排序
            df_sorted = df.sort_values('video_timestamp')
            
            report = {
                'video_info': {
                    'path': video_path,
                    'total_frames_processed': df['frame_number'].nunique(),
                    'total_detections': len(df),
                    'frames_with_detections': df['frame_path'].nunique()
                },
                'detection_timeline': [],
                'statistics': {
                    'average_confidence': float(df['confidence'].mean()),
                    'max_confidence': float(df['confidence'].max()),
                    'min_confidence': float(df['confidence'].min()),
                    'detection_density': len(df) / df['frame_number'].nunique()
                }
            }
            
            # 创建检测时间线
            for _, detection in df_sorted.iterrows():
                timeline_entry = {
                    'timestamp': detection['video_timestamp'],
                    'frame_number': detection['frame_number'],
                    'confidence': detection['confidence'],
                    'bbox': {
                        'x1': detection['bbox_x1'],
                        'y1': detection['bbox_y1'],
                        'x2': detection['bbox_x2'],
                        'y2': detection['bbox_y2']
                    }
                }
                
                if 'latitude' in detection and 'longitude' in detection:
                    timeline_entry['gps'] = {
                        'latitude': detection['latitude'],
                        'longitude': detection['longitude']
                    }
                
                report['detection_timeline'].append(timeline_entry)
            
            # 保存报告
            report_path = Path(output_dir) / "video_detection_report.json"
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"视频检测报告已保存: {report_path}")
            
        except Exception as e:
            self.logger.error(f"生成视频报告失败: {e}")
    
    def _create_detection_video(self, frame_paths: List[str], detections: List[Dict], output_dir: str):
        """创建标注检测结果的视频"""
        try:
            if not frame_paths:
                return
            
            # 创建检测结果字典（按帧路径索引）
            detection_dict = {}
            for detection in detections:
                frame_path = detection['frame_path']
                if frame_path not in detection_dict:
                    detection_dict[frame_path] = []
                detection_dict[frame_path].append(detection)
            
            # 获取第一帧以确定视频尺寸
            first_frame = cv2.imread(frame_paths[0])
            height, width = first_frame.shape[:2]
            
            # 创建视频写入器
            output_video_path = Path(output_dir) / "detection_video.mp4"
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            fps = self.extract_fps
            video_writer = cv2.VideoWriter(str(output_video_path), fourcc, fps, (width, height))
            
            self.logger.info(f"创建检测视频: {output_video_path}")
            
            for frame_path in frame_paths:
                frame = cv2.imread(frame_path)
                
                # 如果该帧有检测结果，绘制边界框
                if frame_path in detection_dict:
                    for detection in detection_dict[frame_path]:
                        x1 = int(detection['bbox_x1'])
                        y1 = int(detection['bbox_y1'])
                        x2 = int(detection['bbox_x2'])
                        y2 = int(detection['bbox_y2'])
                        confidence = detection['confidence']
                        
                        # 绘制边界框
                        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                        
                        # 绘制标签
                        label = f"江豚 {confidence:.2f}"
                        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
                        cv2.rectangle(frame, (x1, y1 - label_size[1] - 10), 
                                    (x1 + label_size[0], y1), (0, 255, 0), -1)
                        cv2.putText(frame, label, (x1, y1 - 5), 
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                
                video_writer.write(frame)
            
            video_writer.release()
            self.logger.info(f"检测视频创建完成: {output_video_path}")
            
        except Exception as e:
            self.logger.error(f"创建检测视频失败: {e}")
    
    def _cleanup_frames(self, frame_paths: List[str]):
        """清理提取的帧文件"""
        try:
            for frame_path in frame_paths:
                if os.path.exists(frame_path):
                    os.remove(frame_path)
            
            self.logger.info(f"已清理 {len(frame_paths)} 个临时帧文件")
            
        except Exception as e:
            self.logger.error(f"清理帧文件失败: {e}")
    
    def batch_process_videos(self, input_dir: str, output_dir: str = None) -> Dict:
        """
        批量处理视频文件
        
        Args:
            input_dir: 输入视频目录
            output_dir: 输出目录
            
        Returns:
            批量处理结果
        """
        input_path = Path(input_dir)
        if not input_path.exists():
            self.logger.error(f"输入目录不存在: {input_dir}")
            return {}
        
        if output_dir is None:
            output_dir = f"batch_video_detection_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 查找视频文件
        video_files = []
        for ext in self.supported_formats:
            video_files.extend(input_path.glob(f"*{ext}"))
            video_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not video_files:
            self.logger.warning(f"在目录 {input_dir} 中未找到支持的视频文件")
            return {}
        
        self.logger.info(f"找到 {len(video_files)} 个视频文件，开始批量处理")
        
        batch_results = {
            'total_videos': len(video_files),
            'processed_videos': 0,
            'total_detections': 0,
            'results': []
        }
        
        for i, video_file in enumerate(video_files):
            self.logger.info(f"处理视频 {i+1}/{len(video_files)}: {video_file.name}")
            
            video_output_dir = Path(output_dir) / video_file.stem
            result = self.process_video(str(video_file), str(video_output_dir))
            
            if result:
                batch_results['processed_videos'] += 1
                batch_results['total_detections'] += result.get('total_detections', 0)
                batch_results['results'].append(result)
        
        # 保存批量处理报告
        report_path = Path(output_dir) / "batch_processing_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(batch_results, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"批量处理完成，报告已保存: {report_path}")
        return batch_results


def main():
    """视频处理命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description='江豚视频检测系统')
    parser.add_argument('--video', '-v', required=True, help='输入视频文件或目录')
    parser.add_argument('--output', '-o', help='输出目录')
    parser.add_argument('--config', '-c', default='config.yaml', help='配置文件路径')
    parser.add_argument('--fps', type=float, help='帧提取率（每秒提取帧数）')
    parser.add_argument('--moviepy', action='store_true', help='使用MoviePy提取帧')
    parser.add_argument('--batch', action='store_true', help='批量处理模式')
    
    args = parser.parse_args()
    
    # 初始化检测器
    detector = FinlessPorpoiseDetector(args.config)
    processor = VideoProcessor(detector)
    
    video_path = Path(args.video)
    
    if args.batch or video_path.is_dir():
        # 批量处理
        results = processor.batch_process_videos(str(video_path), args.output)
        print(f"\n批量处理完成:")
        print(f"处理视频数: {results.get('processed_videos', 0)}/{results.get('total_videos', 0)}")
        print(f"总检测数: {results.get('total_detections', 0)}")
    
    elif video_path.is_file():
        # 单个视频处理
        result = processor.process_video(str(video_path), args.output, args.fps, args.moviepy)
        if result:
            print(f"\n视频处理完成:")
            print(f"提取帧数: {result.get('total_frames', 0)}")
            print(f"检测数量: {result.get('total_detections', 0)}")
            print(f"平均置信度: {result.get('average_confidence', 0):.3f}")
        else:
            print("视频处理失败")
    
    else:
        print(f"错误: 视频路径不存在 - {video_path}")


if __name__ == "__main__":
    main() 