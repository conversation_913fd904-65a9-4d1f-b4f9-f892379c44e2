{"name": "finless-porpoise-detection-ui", "version": "1.0.0", "description": "江豚检测系统 - 现代化Web界面", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "socket.io-client": "^4.7.0", "framer-motion": "^10.16.0", "lucide-react": "^0.294.0", "recharts": "^2.8.0", "react-dropzone": "^14.2.0", "@headlessui/react": "^1.7.0"}, "devDependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.0", "eslint": "^8.52.0", "eslint-config-next": "^14.0.0", "postcss": "^8.4.0", "tailwindcss": "^4.1.0", "typescript": "^5.2.0"}}