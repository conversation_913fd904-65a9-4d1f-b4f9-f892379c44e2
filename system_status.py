#!/usr/bin/env python3
"""
江豚检测系统 - 系统状态检查
检查系统的整体状态并生成诊断报告
"""

import os
import sys
import platform
import importlib.util
import subprocess
from pathlib import Path
import json
from datetime import datetime
import shutil

def check_module(module_name):
    """检查模块是否已安装"""
    return importlib.util.find_spec(module_name) is not None

def get_module_version(module_name):
    """获取模块版本"""
    try:
        # 使用importlib.metadata获取版本信息
        import importlib.metadata
        
        # 包名映射
        package_map = {
            "torch": "torch",
            "ultralytics": "ultralytics", 
            "sahi": "sahi",
            "opencv": "opencv-python",
            "flask": "flask",
            "pandas": "pandas",
            "numpy": "numpy",
            "pillow": "Pillow",
            "exifread": "ExifRead",
            "piexif": "piexif",
            "utm": "utm",
            "geopy": "geopy"
        }
        
        package_name = package_map.get(module_name, module_name)
        try:
            return importlib.metadata.version(package_name)
        except importlib.metadata.PackageNotFoundError:
            # 如果通过包名找不到，尝试导入模块获取版本
            module = importlib.import_module(module_name)
            version = getattr(module, "__version__", "未知版本")
            return version
    except ImportError:
        return "未安装"
    except Exception:
        return "未知版本"

def check_gpu():
    """检查GPU状态"""
    gpu_info = {
        "available": False,
        "type": "CPU",
        "details": {}
    }
    
    # 检查CUDA
    try:
        import torch
        gpu_info["available"] = torch.cuda.is_available()
        if gpu_info["available"]:
            gpu_info["type"] = "CUDA"
            gpu_info["details"]["device_count"] = torch.cuda.device_count()
            gpu_info["details"]["device_name"] = torch.cuda.get_device_name(0)
            gpu_info["details"]["cuda_version"] = torch.version.cuda
            gpu_info["details"]["pytorch_version"] = torch.__version__
        else:
            gpu_info["details"]["pytorch_version"] = torch.__version__
            gpu_info["details"]["reason"] = "CUDA不可用或未安装CUDA版本的PyTorch"
    except ImportError:
        gpu_info["details"]["reason"] = "PyTorch未安装"
    except Exception as e:
        gpu_info["details"]["error"] = str(e)
    
    return gpu_info

def check_files():
    """检查关键文件是否存在"""
    files_status = {}
    
    key_files = [
        "best.pt",
        "config.yaml",
        "src/detector.py",
        "src/model_trainer.py",
        "src/video_processor.py",
        "web_app.py",
        "enhanced_web_app.py",
        "fix_dependencies.py",
        "start.py"
    ]
    
    for file_path in key_files:
        path = Path(file_path)
        if path.exists():
            files_status[file_path] = {
                "exists": True,
                "size": path.stat().st_size,
                "modified": datetime.fromtimestamp(path.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')
            }
        else:
            files_status[file_path] = {
                "exists": False
            }
    
    return files_status

def check_directories():
    """检查关键目录是否存在"""
    dir_status = {}
    
    key_dirs = [
        "src",
        "uploads",
        "web_results",
        "static",
        "templates"
    ]
    
    for dir_path in key_dirs:
        path = Path(dir_path)
        if path.exists() and path.is_dir():
            dir_status[dir_path] = {
                "exists": True,
                "item_count": len(list(path.glob('*')))
            }
        else:
            dir_status[dir_path] = {
                "exists": False
            }
    
    return dir_status

def get_system_info():
    """获取系统信息"""
    # 获取conda环境信息
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', '未知')
    python_path = sys.executable
    
    return {
        "platform": platform.platform(),
        "python_version": platform.python_version(),
        "python_path": python_path,
        "conda_environment": conda_env,
        "processor": platform.processor(),
        "machine": platform.machine(),
        "hostname": platform.node(),
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }

def check_detector():
    """检查检测器状态"""
    detector_status = {
        "importable": False,
        "model_loadable": False,
        "error": None
    }
    
    try:
        sys.path.insert(0, str(Path(__file__).parent / "src"))
        from src.detector import FinlessPorpoiseDetector
        detector_status["importable"] = True
        
        try:
            detector = FinlessPorpoiseDetector()
            detector_status["model_loadable"] = detector.yolo_model is not None
        except Exception as e:
            detector_status["error"] = str(e)
    except ImportError as e:
        detector_status["error"] = str(e)
    
    return detector_status

def check_web_app():
    """检查Web应用状态"""
    web_status = {
        "standard_app": {
            "exists": Path("web_app.py").exists(),
            "size": Path("web_app.py").stat().st_size if Path("web_app.py").exists() else 0
        },
        "enhanced_app": {
            "exists": Path("enhanced_web_app.py").exists(),
            "size": Path("enhanced_web_app.py").stat().st_size if Path("enhanced_web_app.py").exists() else 0
        },
        "templates": {
            "exists": Path("templates").exists(),
            "files": [f.name for f in Path("templates").glob("*.html")] if Path("templates").exists() else []
        }
    }
    
    return web_status

def generate_report():
    """生成系统状态报告"""
    report = {
        "system_info": get_system_info(),
        "dependencies": {
            "torch": get_module_version("torch"),
            "ultralytics": get_module_version("ultralytics"),
            "sahi": get_module_version("sahi"),
            "opencv": get_module_version("cv2"),
            "flask": get_module_version("flask"),
            "pandas": get_module_version("pandas"),
            "numpy": get_module_version("numpy"),
            "pillow": get_module_version("PIL"),
            "exifread": get_module_version("exifread"),
            "piexif": get_module_version("piexif"),
            "utm": get_module_version("utm"),
            "geopy": get_module_version("geopy")
        },
        "gpu_info": check_gpu(),
        "files_status": check_files(),
        "directories": check_directories(),
        "detector_status": check_detector(),
        "web_app_status": check_web_app()
    }
    
    # 检查磁盘空间
    try:
        total, used, free = shutil.disk_usage("/")
        report["disk_space"] = {
            "total_gb": total // (2**30),
            "used_gb": used // (2**30),
            "free_gb": free // (2**30),
            "percent_used": used * 100 / total
        }
    except Exception as e:
        report["disk_space"] = {"error": str(e)}
    
    return report

def main():
    """主函数"""
    print("\n" + "="*50)
    print("🐋 江豚检测系统 - 系统状态检查")
    print("="*50)
    
    report = generate_report()
    
    # 打印摘要
    print("\n系统信息:")
    print(f"  平台: {report['system_info']['platform']}")
    print(f"  Python版本: {report['system_info']['python_version']}")
    print(f"  Python路径: {report['system_info']['python_path']}")
    print(f"  Conda环境: {report['system_info']['conda_environment']}")
    print(f"  时间戳: {report['system_info']['timestamp']}")
    
    # 检查是否在推荐的环境中
    if report['system_info']['conda_environment'] != 'yolov11':
        print(f"  ⚠️ 推荐使用 'yolov11' 环境")
        print(f"  💡 请运行: conda activate yolov11")
    
    print("\n关键依赖:")
    for name, version in report['dependencies'].items():
        if version != "未安装":
            print(f"  ✅ {name}: {version}")
        else:
            print(f"  ❌ {name}: 未安装")
    
    print("\nGPU状态:")
    if report['gpu_info']['available']:
        print(f"  ✅ GPU可用: {report['gpu_info']['type']}")
        print(f"  设备: {report['gpu_info']['details'].get('device_name', '未知')}")
    else:
        print("  ❌ GPU不可用，将使用CPU模式")
    
    print("\n检测器状态:")
    if report['detector_status']['importable']:
        print("  ✅ 检测器模块可导入")
        if report['detector_status']['model_loadable']:
            print("  ✅ YOLO模型可加载")
        else:
            print("  ❌ YOLO模型无法加载")
            if report['detector_status']['error']:
                print(f"  错误: {report['detector_status']['error']}")
    else:
        print("  ❌ 检测器模块无法导入")
        if report['detector_status']['error']:
            print(f"  错误: {report['detector_status']['error']}")
    
    print("\n关键文件:")
    for file_path, status in report['files_status'].items():
        if status['exists']:
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} (缺失)")
    
    # 保存完整报告
    report_path = f"system_status_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n完整报告已保存至: {report_path}")
    print("\n" + "="*50)

if __name__ == "__main__":
    main() 