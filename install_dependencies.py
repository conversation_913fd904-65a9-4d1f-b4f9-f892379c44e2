#!/usr/bin/env python3
"""
铜陵无人机数据检测系统 - 依赖安装脚本
"""

import subprocess
import sys
import os
from pathlib import Path

def install_package(package_name):
    """安装单个包"""
    try:
        print(f"正在安装 {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 安装 {package_name} 时出错: {e}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name.replace('-', '_'))
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    print("🐋 铜陵无人机数据检测系统 - 依赖安装")
    print("="*50)
    
    # 必需的依赖包
required_packages = [
    'ultralytics>=8.0.0',
    'sahi>=0.11.0',
    'opencv-python>=4.8.0',
    'pandas>=1.5.0',
    'numpy>=1.21.0',
    'matplotlib>=3.5.0',
    'seaborn>=0.11.0',
    'folium>=0.14.0',
    'exifread>=3.0.0',
    'torch>=1.13.0',
    'torchvision>=0.14.0',
    'tensorrt>=8.0.0',
    'pycuda>=2022.1',
    'tqdm>=4.64.0',
    'psutil>=5.9.0',
    'ffmpeg-python>=0.2.0' # 新增 ffmpeg-python
]
    
    # 可选的依赖包
    optional_packages = [
        'jupyter',
        'ipywidgets',
        'plotly',
        'bokeh'
    ]
    
    print("检查已安装的包...")
    installed_packages = []
    missing_packages = []
    
    for package in required_packages:
        package_name = package.split('>=')[0].split('==')[0]
        if check_package(package_name):
            print(f"✅ {package_name} 已安装")
            installed_packages.append(package_name)
        else:
            print(f"❌ {package_name} 未安装")
            missing_packages.append(package)
    
    if not missing_packages:
        print("\n🎉 所有必需依赖已安装！")
        return True
    
    print(f"\n需要安装 {len(missing_packages)} 个包:")
    for package in missing_packages:
        print(f"  - {package}")
    
    # 询问是否安装
    while True:
        choice = input("\n是否安装缺失的依赖包？(y/n): ").strip().lower()
        if choice in ['y', 'n']:
            break
        print("请输入 y 或 n")
    
    if choice == 'n':
        print("取消安装")
        return False
    
    # 安装缺失的包
    print("\n开始安装依赖包...")
    failed_packages = []
    
    for package in missing_packages:
        if not install_package(package):
            failed_packages.append(package)
    
    # 检查安装结果
    if failed_packages:
        print(f"\n❌ 以下包安装失败:")
        for package in failed_packages:
            print(f"  - {package}")
        print("\n请手动安装这些包:")
        print("pip install " + " ".join(failed_packages))
        return False
    else:
        print("\n🎉 所有依赖包安装成功！")
        
        # 询问是否安装可选包
        print("\n可选依赖包:")
        for package in optional_packages:
            print(f"  - {package}")
        
        while True:
            choice = input("\n是否安装可选依赖包？(y/n): ").strip().lower()
            if choice in ['y', 'n']:
                break
            print("请输入 y 或 n")
        
        if choice == 'y':
            print("\n安装可选依赖包...")
            for package in optional_packages:
                install_package(package)
        
        return True

def check_cuda():
    """检查CUDA支持"""
    print("\n检查CUDA支持...")
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ CUDA可用，设备数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"  设备 {i}: {torch.cuda.get_device_name(i)}")
        else:
            print("⚠️ CUDA不可用，将使用CPU")
    except ImportError:
        print("❌ PyTorch未安装，无法检查CUDA")

def check_system():
    """检查系统信息"""
    print("\n系统信息:")
    print(f"Python版本: {sys.version}")
    print(f"操作系统: {os.name}")
    
    # 检查GPU
    try:
        import torch
        if torch.cuda.is_available():
            print(f"GPU: {torch.cuda.get_device_name(0)}")
            print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    except:
        print("GPU: 无法检测")

if __name__ == "__main__":
    print("🐋 铜陵无人机数据检测系统 - 依赖安装")
    print("="*50)
    
    # 检查系统信息
    check_system()
    
    # 安装依赖
    success = main()
    
    # 检查CUDA
    check_cuda()
    
    print("\n" + "="*50)
    if success:
        print("✅ 依赖安装完成！")
        print("现在可以运行检测脚本了")
    else:
        print("❌ 依赖安装失败！")
        print("请手动安装缺失的依赖包")
    print("="*50) 