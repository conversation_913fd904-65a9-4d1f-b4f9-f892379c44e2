"""
江豚检测器核心模块
基于YOLOv11和SAHI技术的江豚检测与GPS定位系统
"""

import os
import cv2
import numpy as np
import pandas as pd
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Any
import yaml
import logging
from datetime import datetime
import json
import importlib.util
import sys

# 检查模块是否可用的函数
def is_module_available(module_name):
    """检查指定的模块是否可用"""
    module_spec = importlib.util.find_spec(module_name)
    return module_spec is not None

# 更安全的模块导入方式
ULTRALYTICS_AVAILABLE = is_module_available("ultralytics")
if ULTRALYTICS_AVAILABLE:
    try:
        from ultralytics import YOLO
        print("✅ Ultralytics 导入成功")
    except ImportError as e:
        ULTRALYTICS_AVAILABLE = False
        print(f"❌ Ultralytics 导入失败: {e}")
else:
    print("❌ ultralytics未安装，请运行: pip install ultralytics")

SAHI_AVAILABLE = is_module_available("sahi")
if SAHI_AVAILABLE:
    try:
        from sahi import AutoDetectionModel
        from sahi.predict import get_sliced_prediction
        print("✅ SAHI 导入成功")
    except ImportError as e:
        SAHI_AVAILABLE = False
        print(f"❌ SAHI 导入失败: {e}")
else:
    print("❌ sahi未安装，请运行: pip install sahi")

try:
    import exifread
    import piexif
    EXIF_AVAILABLE = True
except ImportError:
    EXIF_AVAILABLE = False
    print("❌ EXIF库未安装，请运行: pip install exifread piexif")

try:
    import utm
    import geopy.distance
    GPS_AVAILABLE = True
except ImportError:
    GPS_AVAILABLE = False
    print("❌ GPS库未安装，请运行: pip install utm geopy")


class FinlessPorpoiseDetector:
    """江豚检测器主类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化江豚检测器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
        
        # 初始化模型
        self.yolo_model = None
        self.sahi_model = None
        
        # DJI Mavic 3 相机参数
        self.camera_params = self.config['mavic3_camera']
        
        # 检测结果
        self.detection_results = []
        
        self._initialize_models()
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"警告: 无法加载配置文件 {config_path}, 使用默认配置: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'model': {
                'weights_path': 'best.pt',
                'confidence_threshold': 0.25,
                'iou_threshold': 0.45,
                'device': 'auto'
            },
            'sahi': {
                'enabled': True,
                'slice_height': 1280,
                'slice_width': 1280,
                'overlap_height_ratio': 0.2,
                'overlap_width_ratio': 0.2
            },
            'mavic3_camera': {
                'sensor_width_mm': 17.3,
                'sensor_height_mm': 13.0,
                'focal_length_mm': 24.0,
                'image_width_px': 5280,
                'image_height_px': 3956
            },
            'gps': {
                'altitude_offset': 0.0,
                'coordinate_system': 'WGS84',
                'precision_decimal': 6
            }
        }
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('FinlessPorpoiseDetector')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # 控制台输出
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 日志格式
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
            
            # 文件输出
            if self.config.get('logging', {}).get('save_to_file', True):
                file_handler = logging.FileHandler(
                    f"detection_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log",
                    encoding='utf-8'
                )
                file_handler.setLevel(logging.INFO)
                file_handler.setFormatter(formatter)
                logger.addHandler(file_handler)
        
        return logger
    
    def _initialize_models(self):
        """初始化检测模型"""
        if not ULTRALYTICS_AVAILABLE:
            self.logger.error("ultralytics未安装，无法加载YOLO模型")
            return
        
        try:
            # 加载YOLO模型
            weights_path = self.config['model']['weights_path']
            if not os.path.exists(weights_path):
                self.logger.error(f"模型权重文件不存在: {weights_path}")
                return
            
            # 强制重新导入YOLO
            if "ultralytics.YOLO" in sys.modules:
                del sys.modules["ultralytics.YOLO"]
            
            # 尝试直接从文件加载模型
            self.yolo_model = YOLO(weights_path)
            self.logger.info(f"成功加载YOLO模型: {weights_path}")
            
            # 初始化SAHI模型
            if SAHI_AVAILABLE and self.config['sahi']['enabled']:
                self.sahi_model = AutoDetectionModel.from_pretrained(
                    model_type='yolov8',
                    model_path=weights_path,
                    confidence_threshold=self.config['model']['confidence_threshold'],
                    device=self.config['model']['device']
                )
                self.logger.info("成功初始化SAHI切片检测模型")
            
        except Exception as e:
            self.logger.error(f"模型初始化失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
    
    def extract_gps_info(self, image_path: str) -> Dict[str, Any]:
        """
        从图片EXIF信息中提取GPS数据
        
        Args:
            image_path: 图片路径
            
        Returns:
            包含GPS信息的字典
        """
        if not EXIF_AVAILABLE:
            self.logger.warning("EXIF库未安装，无法提取GPS信息")
            return {}
        
        try:
            gps_info = {}
            
            # 使用exifread读取EXIF信息
            with open(image_path, 'rb') as f:
                tags = exifread.process_file(f)
            
            # 提取GPS坐标
            if 'GPS GPSLatitude' in tags and 'GPS GPSLongitude' in tags:
                # 纬度
                lat_ref = str(tags.get('GPS GPSLatitudeRef', 'N'))
                lat_values = tags['GPS GPSLatitude'].values
                latitude = self._dms_to_decimal(lat_values, lat_ref)
                
                # 经度
                lon_ref = str(tags.get('GPS GPSLongitudeRef', 'E'))
                lon_values = tags['GPS GPSLongitude'].values
                longitude = self._dms_to_decimal(lon_values, lon_ref)
                
                gps_info['latitude'] = latitude
                gps_info['longitude'] = longitude
            
            # 提取高度信息
            if 'GPS GPSAltitude' in tags:
                altitude = float(tags['GPS GPSAltitude'].values[0])
                gps_info['altitude'] = altitude
            
            # 提取相机信息
            if 'EXIF FocalLength' in tags:
                focal_length = float(tags['EXIF FocalLength'].values[0])
                gps_info['focal_length'] = focal_length
            
            # 提取时间信息
            if 'GPS GPSTimeStamp' in tags:
                gps_time = tags['GPS GPSTimeStamp']
                gps_info['gps_time'] = str(gps_time)
            
            if 'EXIF DateTime' in tags:
                datetime_str = str(tags['EXIF DateTime'])
                gps_info['datetime'] = datetime_str
            
            return gps_info
            
        except Exception as e:
            self.logger.error(f"提取GPS信息失败 {image_path}: {e}")
            return {}
    
    def _dms_to_decimal(self, dms_values: List, ref: str) -> float:
        """
        将度分秒格式转换为十进制度数
        
        Args:
            dms_values: 度分秒值列表
            ref: 参考方向 (N/S/E/W)
            
        Returns:
            十进制度数
        """
        degrees = float(dms_values[0])
        minutes = float(dms_values[1])
        seconds = float(dms_values[2])
        
        decimal = degrees + minutes/60 + seconds/3600
        
        if ref in ['S', 'W']:
            decimal = -decimal
        
        return decimal
    
    def calculate_target_gps(self, image_path: str, bbox: Tuple[int, int, int, int], 
                           gps_info: Dict[str, Any]) -> Dict[str, float]:
        """
        计算检测目标的GPS坐标
        
        Args:
            image_path: 图片路径
            bbox: 边界框坐标 (x1, y1, x2, y2)
            gps_info: 图片GPS信息
            
        Returns:
            目标GPS坐标字典
        """
        if not GPS_AVAILABLE:
            self.logger.warning("GPS库未安装，无法计算目标GPS坐标")
            return {}
        
        try:
            # 检查必要的GPS信息
            required_keys = ['latitude', 'longitude', 'altitude']
            if not all(key in gps_info for key in required_keys):
                self.logger.warning("GPS信息不完整，无法计算目标坐标")
                return gps_info
            
            # 获取图片尺寸
            image = cv2.imread(image_path)
            img_height, img_width = image.shape[:2]
            
            # 计算目标中心点在图像中的位置
            x1, y1, x2, y2 = bbox
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2
            
            # 计算相对于图像中心的偏移（像素）
            offset_x = center_x - img_width / 2
            offset_y = center_y - img_height / 2
            
            # 获取相机参数
            focal_length = gps_info.get('focal_length', self.camera_params['focal_length_mm'])
            sensor_width = self.camera_params['sensor_width_mm']
            sensor_height = self.camera_params['sensor_height_mm']
            
            # 计算地面覆盖范围
            altitude = gps_info['altitude'] + self.config['gps']['altitude_offset']
            
            # 地面像素分辨率 (米/像素)
            ground_width = 2 * altitude * sensor_width / focal_length / 1000  # 转换为米
            ground_height = 2 * altitude * sensor_height / focal_length / 1000
            
            pixel_res_x = ground_width / img_width
            pixel_res_y = ground_height / img_height
            
            # 计算地面偏移距离（米）
            ground_offset_x = offset_x * pixel_res_x
            ground_offset_y = offset_y * pixel_res_y
            
            # 使用UTM坐标系进行精确计算
            drone_lat = gps_info['latitude']
            drone_lon = gps_info['longitude']
            
            # 转换为UTM坐标
            utm_x, utm_y, zone_num, zone_letter = utm.from_latlon(drone_lat, drone_lon)
            
            # 添加偏移（注意Y轴方向）
            target_utm_x = utm_x + ground_offset_x
            target_utm_y = utm_y - ground_offset_y  # 图像Y轴向下，UTM Y轴向上
            
            # 转换回经纬度
            target_lat, target_lon = utm.to_latlon(
                target_utm_x, target_utm_y, zone_num, zone_letter
            )
            
            # 精度控制
            precision = self.config['gps']['precision_decimal']
            target_lat = round(target_lat, precision)
            target_lon = round(target_lon, precision)
            
            return {
                'latitude': target_lat,
                'longitude': target_lon,
                'altitude': gps_info['altitude'],
                'ground_pixel_res_x': round(pixel_res_x, 4),
                'ground_pixel_res_y': round(pixel_res_y, 4),
                'ground_coverage_width': round(ground_width, 2),
                'ground_coverage_height': round(ground_height, 2)
            }
            
        except Exception as e:
            self.logger.error(f"计算目标GPS坐标失败: {e}")
            return gps_info
    
    def detect_image(self, image_path: str, use_sahi: bool = None) -> List[Dict]:
        """
        检测单张图片中的江豚
        
        Args:
            image_path: 图片路径
            use_sahi: 是否使用SAHI切片检测
            
        Returns:
            检测结果列表
        """
        if self.yolo_model is None:
            self.logger.error("YOLO模型未初始化")
            return []
        
        if use_sahi is None:
            use_sahi = self.config['sahi']['enabled'] and SAHI_AVAILABLE
        
        self.logger.info(f"开始检测图片: {image_path}")
        
        try:
            # 提取GPS信息
            gps_info = self.extract_gps_info(image_path)
            
            detections = []
            
            if use_sahi and self.sahi_model:
                # SAHI切片检测
                detections = self._detect_with_sahi(image_path, gps_info)
            else:
                # 标准YOLO检测
                detections = self._detect_with_yolo(image_path, gps_info)
            
            self.logger.info(f"检测完成，发现 {len(detections)} 个目标")
            
            # 保存检测结果
            self.detection_results.extend(detections)
            
            return detections
            
        except Exception as e:
            self.logger.error(f"图片检测失败 {image_path}: {e}")
            return []
    
    def _detect_with_yolo(self, image_path: str, gps_info: Dict) -> List[Dict]:
        """使用标准YOLO进行检测"""
        results = self.yolo_model(
            image_path,
            conf=self.config['model']['confidence_threshold'],
            iou=self.config['model']['iou_threshold']
        )
        
        detections = []
        
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for i, box in enumerate(boxes):
                    # 获取边界框坐标
                    x1, y1, x2, y2 = box.xyxy[0].tolist()
                    confidence = float(box.conf[0])
                    class_id = int(box.cls[0])
                    
                    # 计算目标GPS坐标
                    target_gps = self.calculate_target_gps(
                        image_path, (x1, y1, x2, y2), gps_info
                    )
                    
                    detection = {
                        'image_path': image_path,
                        'detection_id': f"{Path(image_path).stem}_{i}",
                        'class_id': class_id,
                        'class_name': 'finless_porpoise',
                        'confidence': round(confidence, 4),
                        'bbox_x1': round(x1, 2),
                        'bbox_y1': round(y1, 2),
                        'bbox_x2': round(x2, 2),
                        'bbox_y2': round(y2, 2),
                        'bbox_width': round(x2 - x1, 2),
                        'bbox_height': round(y2 - y1, 2),
                        'detection_method': 'YOLO',
                        'timestamp': datetime.now().isoformat(),
                        **target_gps
                    }
                    
                    detections.append(detection)
        
        return detections
    
    def _detect_with_sahi(self, image_path: str, gps_info: Dict) -> List[Dict]:
        """使用SAHI切片检测"""
        sahi_config = self.config['sahi']
        
        result = get_sliced_prediction(
            image_path,
            self.sahi_model,
            slice_height=sahi_config['slice_height'],
            slice_width=sahi_config['slice_width'],
            overlap_height_ratio=sahi_config['overlap_height_ratio'],
            overlap_width_ratio=sahi_config['overlap_width_ratio'],
            postprocess_type=sahi_config.get('postprocess_type', 'NMS'),
            postprocess_match_metric=sahi_config.get('postprocess_match_metric', 'IOU'),
            postprocess_match_threshold=sahi_config.get('postprocess_match_threshold', 0.5)
        )
        
        detections = []
        
        for i, detection in enumerate(result.object_prediction_list):
            bbox = detection.bbox
            x1, y1, x2, y2 = bbox.minx, bbox.miny, bbox.maxx, bbox.maxy
            confidence = detection.score.value
            class_name = detection.category.name
            
            # 计算目标GPS坐标
            target_gps = self.calculate_target_gps(
                image_path, (x1, y1, x2, y2), gps_info
            )
            
            detection_dict = {
                'image_path': image_path,
                'detection_id': f"{Path(image_path).stem}_sahi_{i}",
                'class_id': 0,  # 江豚类别ID
                'class_name': class_name,
                'confidence': round(confidence, 4),
                'bbox_x1': round(x1, 2),
                'bbox_y1': round(y1, 2),
                'bbox_x2': round(x2, 2),
                'bbox_y2': round(y2, 2),
                'bbox_width': round(x2 - x1, 2),
                'bbox_height': round(y2 - y1, 2),
                'detection_method': 'SAHI',
                'slice_height': sahi_config['slice_height'],
                'slice_width': sahi_config['slice_width'],
                'timestamp': datetime.now().isoformat(),
                **target_gps
            }
            
            detections.append(detection_dict)
        
        return detections
    
    def batch_detect(self, input_dir: str, output_dir: str = None) -> pd.DataFrame:
        """
        批量检测图片中的江豚
        
        Args:
            input_dir: 输入图片目录
            output_dir: 输出结果目录
            
        Returns:
            检测结果DataFrame
        """
        if output_dir is None:
            output_dir = f"detection_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        Path(output_dir).mkdir(exist_ok=True)
        
        # 支持的图片格式
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
        
        # 查找所有图片文件
        image_files = []
        for ext in image_extensions:
            image_files.extend(Path(input_dir).glob(f"*{ext}"))
            image_files.extend(Path(input_dir).glob(f"*{ext.upper()}"))
        
        self.logger.info(f"找到 {len(image_files)} 张图片，开始批量检测")
        
        all_detections = []
        
        for i, image_path in enumerate(image_files):
            self.logger.info(f"处理进度: {i+1}/{len(image_files)} - {image_path.name}")
            
            detections = self.detect_image(str(image_path))
            all_detections.extend(detections)
            
            # 可选：保存标注图片
            if self.config.get('output', {}).get('save_annotated_images', True):
                self._save_annotated_image(str(image_path), detections, output_dir)
        
        # 转换为DataFrame并保存
        df = pd.DataFrame(all_detections)
        
        if not df.empty:
            csv_path = Path(output_dir) / "detection_results.csv"
            df.to_csv(csv_path, index=False, encoding='utf-8-sig')
            self.logger.info(f"检测结果已保存到: {csv_path}")
            
            # 生成统计报告
            self._generate_summary_report(df, output_dir)
        
        return df
    
    def _save_annotated_image(self, image_path: str, detections: List[Dict], output_dir: str):
        """保存标注后的图片"""
        try:
            image = cv2.imread(image_path)
            
            for detection in detections:
                x1 = int(detection['bbox_x1'])
                y1 = int(detection['bbox_y1'])
                x2 = int(detection['bbox_x2'])
                y2 = int(detection['bbox_y2'])
                confidence = detection['confidence']
                
                # 绘制边界框
                color = tuple(self.config.get('visualization', {}).get('bbox_color', [0, 255, 0]))
                thickness = self.config.get('visualization', {}).get('bbox_thickness', 2)
                
                cv2.rectangle(image, (x1, y1), (x2, y2), color, thickness)
                
                # 绘制标签
                if self.config.get('visualization', {}).get('show_confidence', True):
                    label = f"江豚 {confidence:.2f}"
                    label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
                    
                    cv2.rectangle(image, (x1, y1 - label_size[1] - 10), 
                                (x1 + label_size[0], y1), color, -1)
                    cv2.putText(image, label, (x1, y1 - 5), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # 保存标注图片
            output_path = Path(output_dir) / "annotated_images" / Path(image_path).name
            output_path.parent.mkdir(exist_ok=True)
            cv2.imwrite(str(output_path), image)
            
        except Exception as e:
            self.logger.error(f"保存标注图片失败 {image_path}: {e}")
    
    def _generate_summary_report(self, df: pd.DataFrame, output_dir: str):
        """生成检测统计报告"""
        try:
            report = {
                'summary': {
                    'total_images': df['image_path'].nunique(),
                    'total_detections': len(df),
                    'images_with_detections': df[df['confidence'] > 0]['image_path'].nunique(),
                    'average_confidence': float(df['confidence'].mean()),
                    'max_confidence': float(df['confidence'].max()),
                    'min_confidence': float(df['confidence'].min())
                },
                'detection_methods': df['detection_method'].value_counts().to_dict(),
                'confidence_distribution': {
                    'high_confidence (>0.8)': len(df[df['confidence'] > 0.8]),
                    'medium_confidence (0.5-0.8)': len(df[(df['confidence'] >= 0.5) & (df['confidence'] <= 0.8)]),
                    'low_confidence (<0.5)': len(df[df['confidence'] < 0.5])
                }
            }
            
            # 保存报告
            report_path = Path(output_dir) / "detection_report.json"
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"统计报告已保存到: {report_path}")
            
        except Exception as e:
            self.logger.error(f"生成统计报告失败: {e}")


def main():
    """主函数 - 命令行界面"""
    import argparse
    
    parser = argparse.ArgumentParser(description='江豚检测系统')
    parser.add_argument('--input', '-i', required=True, help='输入图片或目录路径')
    parser.add_argument('--output', '-o', help='输出目录路径')
    parser.add_argument('--config', '-c', default='config.yaml', help='配置文件路径')
    parser.add_argument('--sahi', action='store_true', help='使用SAHI切片检测')
    parser.add_argument('--no-sahi', action='store_true', help='不使用SAHI切片检测')
    
    args = parser.parse_args()
    
    # 初始化检测器
    detector = FinlessPorpoiseDetector(args.config)
    
    input_path = Path(args.input)
    
    if input_path.is_file():
        # 单张图片检测
        use_sahi = args.sahi if args.sahi else (not args.no_sahi if args.no_sahi else None)
        results = detector.detect_image(str(input_path), use_sahi)
        
        if results:
            print(f"\n检测到 {len(results)} 个江豚目标:")
            for i, result in enumerate(results, 1):
                print(f"{i}. 置信度: {result['confidence']:.3f}, "
                      f"位置: ({result.get('latitude', 'N/A')}, {result.get('longitude', 'N/A')})")
        else:
            print("未检测到江豚目标")
    
    elif input_path.is_dir():
        # 批量检测
        df = detector.batch_detect(str(input_path), args.output)
        
        if not df.empty:
            print(f"\n批量检测完成:")
            print(f"总共处理图片: {df['image_path'].nunique()}")
            print(f"检测到目标: {len(df)}")
            print(f"平均置信度: {df['confidence'].mean():.3f}")
        else:
            print("批量检测未发现任何目标")
    
    else:
        print(f"错误: 输入路径不存在 - {input_path}")


if __name__ == "__main__":
    main() 