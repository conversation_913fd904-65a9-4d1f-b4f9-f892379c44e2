# 江豚检测系统 - 快速开始指南

## 🚀 快速启动

### 🐍 Conda环境准备
**重要**: 请确保在 `yolov11` conda环境中运行系统

```bash
# 激活环境
conda activate yolov11

# 验证环境
python --version
```

### 方法1: Conda环境启动（推荐）
双击 `start_conda.bat` 文件，会自动激活yolov11环境

### 方法2: 一键启动
```bash
# 确保先激活环境
conda activate yolov11
python run.py
```

### 方法3: 直接启动Web应用
```bash
# 确保先激活环境
conda activate yolov11
python quick_start.py
```

### 方法4: 通用启动
双击 `start_system.bat` 文件

## 📋 系统状态

从您的运行日志可以看出，系统已经完全正常工作：

✅ **所有依赖已安装**
- torch: 2.0.0+cpu
- ultralytics: 8.3.161
- sahi: 0.11.28
- opencv: 4.11.0
- flask: 3.1.1

✅ **检测器状态正常**
- YOLO模型成功加载
- SAHI切片检测模型初始化成功
- 检测器模块可以正常导入

✅ **所有关键文件存在**
- best.pt (模型文件)
- config.yaml (配置文件)
- 所有源代码文件

## 🌐 使用Web应用

1. 启动Web应用后，打开浏览器访问：`http://localhost:5000`

2. 上传测试图像：
   - 支持 JPG、PNG 格式
   - 建议图像分辨率不超过 4K
   - 如果图像包含GPS信息，系统会自动提取

3. 调整检测参数：
   - **置信度阈值**: 默认0.25，可调整范围0.1-0.9
   - **使用SAHI**: 对于小目标检测建议开启

4. 点击"开始检测"，等待结果

## 🧪 测试建议

由于系统提示"uploads目录中没有测试图像"，建议您：

1. 在项目根目录创建 `uploads` 文件夹（如果不存在）
2. 放入一些测试图像
3. 运行 `python test_detector.py` 进行测试

## 📊 检测结果

系统会显示：
- 检测到的江豚数量
- 每个检测目标的置信度
- 边界框坐标
- GPS位置信息（如果可用）
- 标注后的图像

## 🔧 故障排除

如果遇到问题：

1. 运行系统诊断：
   ```bash
   python system_status.py
   ```

2. 修复依赖问题：
   ```bash
   python fix_dependencies.py
   ```

3. 检查日志文件：
   - `detection_log_*.log`
   - `system_status_*.json`

## 💡 使用技巧

1. **提高检测精度**：
   - 降低置信度阈值（如0.15）
   - 启用SAHI切片检测
   - 使用高质量图像

2. **加快检测速度**：
   - 提高置信度阈值（如0.4）
   - 关闭SAHI切片检测
   - 使用较小分辨率图像

3. **批量处理**：
   - 将多张图像放入 `uploads` 目录
   - 使用 `python test_detector.py` 批量测试

## 🎯 下一步

系统已经准备就绪，您可以：

1. 立即开始使用Web界面进行检测
2. 添加测试图像到 `uploads` 目录
3. 根据需要调整 `config.yaml` 中的参数
4. 查看检测结果和日志文件

祝您使用愉快！🐋 