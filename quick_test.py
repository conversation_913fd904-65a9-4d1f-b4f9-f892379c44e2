#!/usr/bin/env python3
"""
江豚检测系统 - 快速测试脚本
使用性能优化配置进行快速检测测试
"""

import os
import sys
from pathlib import Path
import time

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def check_conda_environment():
    """检查是否在正确的conda环境中"""
    try:
        conda_env = os.environ.get('CONDA_DEFAULT_ENV', '')
        if conda_env != 'yolov11':
            print(f"⚠️ 当前环境: {conda_env if conda_env else '未知'}")
            print("🔧 推荐使用 'yolov11' 环境")
            print("💡 请运行: conda activate yolov11")
            return False
        else:
            print(f"✅ 当前环境: {conda_env}")
            return True
    except Exception as e:
        print(f"⚠️ 环境检查失败: {e}")
        return False

def main():
    """主函数"""
    print("\n" + "="*50)
    print("🐋 江豚检测系统 - 快速测试")
    print("="*50)
    
    # 检查conda环境
    if not check_conda_environment():
        print("请先激活yolov11环境")
        return
    
    # 检查模型文件
    model_path = Path("best.pt")
    if not model_path.exists():
        print("⚠️ 模型文件不存在: best.pt")
        return
    
    # 检查快速配置文件
    fast_config = Path("config_fast.yaml")
    if not fast_config.exists():
        print("⚠️ 快速配置文件不存在: config_fast.yaml")
        print("使用默认配置文件: config.yaml")
        config_file = "config.yaml"
    else:
        config_file = "config_fast.yaml"
        print(f"✅ 使用快速配置: {config_file}")
    
    try:
        # 导入检测器
        from src.detector import FinlessPorpoiseDetector
        print("✅ 成功导入检测器模块")
        
        # 初始化检测器
        print("\n🔧 初始化检测器...")
        start_time = time.time()
        detector = FinlessPorpoiseDetector(config_file)
        init_time = time.time() - start_time
        
        if detector.yolo_model is not None:
            print(f"✅ 检测器初始化成功 ({init_time:.2f}秒)")
        else:
            print("❌ YOLO模型未加载")
            return
        
        # 检查测试图像
        uploads_dir = Path("uploads")
        if not uploads_dir.exists():
            uploads_dir.mkdir(exist_ok=True)
            print("✅ 创建uploads目录")
        
        # 查找测试图像
        test_images = list(uploads_dir.glob("*.jpg")) + list(uploads_dir.glob("*.png"))
        
        if not test_images:
            print("⚠️ uploads目录中没有测试图像")
            print("请添加一些图像文件到uploads目录")
            return
        
        print(f"📸 找到 {len(test_images)} 张测试图像")
        
        # 创建结果目录
        results_dir = Path("quick_test_results")
        results_dir.mkdir(exist_ok=True)
        
        # 快速测试第一张图像
        test_image = test_images[0]
        print(f"\n🔍 快速测试图像: {test_image.name}")
        
        # 使用标准YOLO检测（不使用SAHI）
        print("使用标准YOLO检测（快速模式）...")
        start_time = time.time()
        detections = detector.detect_image(str(test_image), use_sahi=False)
        detection_time = time.time() - start_time
        
        print(f"⏱️ 检测耗时: {detection_time:.2f}秒")
        print(f"🎯 检测结果: {len(detections)} 个目标")
        
        if detections:
            for i, detection in enumerate(detections):
                confidence = detection.get('confidence', 0)
                x1 = detection.get('bbox_x1', 0)
                y1 = detection.get('bbox_y1', 0)
                x2 = detection.get('bbox_x2', 0)
                y2 = detection.get('bbox_y2', 0)
                print(f"  目标 {i+1}: 置信度={confidence:.3f}, 位置=({x1:.0f},{y1:.0f},{x2:.0f},{y2:.0f})")
        
        # 保存标注图像
        try:
            detector._save_annotated_image(str(test_image), detections, str(results_dir))
            print(f"✅ 标注图像已保存到: {results_dir}")
        except Exception as e:
            print(f"⚠️ 保存标注图像失败: {e}")
        
        print("\n" + "="*50)
        print("🎉 快速测试完成！")
        print(f"📊 性能统计:")
        print(f"  - 初始化时间: {init_time:.2f}秒")
        print(f"  - 检测时间: {detection_time:.2f}秒")
        print(f"  - 检测目标: {len(detections)}个")
        print("="*50)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    main() 