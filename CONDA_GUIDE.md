# Conda环境使用指南

## 🐍 关于yolov11环境

江豚检测系统需要在名为 `yolov11` 的conda环境中运行，这确保了所有依赖包的版本兼容性。

## 🚀 快速启动

### 最简单的方法
双击 `start_conda.bat` 文件，脚本会自动：
1. 检测并初始化conda
2. 激活yolov11环境
3. 验证Python可用性
4. 提供启动选项

### 手动启动
```bash
# 1. 激活环境
conda activate yolov11

# 2. 验证环境
python --version

# 3. 启动系统
python run.py
```

## 🔧 环境管理

### 检查当前环境
```bash
# 查看当前激活的环境
conda info --envs

# 查看环境中的包
conda list
```

### 如果yolov11环境不存在
```bash
# 创建新环境
conda create -n yolov11 python=3.10

# 激活环境
conda activate yolov11

# 安装依赖
pip install -r requirements.txt
```

### 环境问题排查
```bash
# 查看所有环境
conda env list

# 检查conda版本
conda --version

# 更新conda
conda update conda
```

## 🎯 系统集成

### 自动环境检测
系统启动脚本会自动检测当前环境：
- ✅ 如果在yolov11环境中：正常运行
- ⚠️ 如果在其他环境中：显示警告和建议

### 环境信息显示
运行 `python system_status.py` 会显示：
- 当前conda环境名称
- Python路径
- 环境建议

## 🛠️ 故障排除

### 常见问题

#### 1. conda命令不可用
```bash
# Windows
# 需要在Anaconda Prompt中运行，或者将conda添加到PATH

# 或者使用完整路径
C:\Users\<USER>\anaconda3\Scripts\conda.exe activate yolov11
```

#### 2. 环境激活失败
```bash
# 检查环境是否存在
conda env list

# 如果不存在，创建环境
conda create -n yolov11 python=3.10
```

#### 3. 依赖包问题
```bash
# 在yolov11环境中重新安装依赖
conda activate yolov11
python fix_dependencies.py
```

## 📋 启动脚本对比

| 脚本 | 功能 | 环境处理 |
|------|------|----------|
| `start_conda.bat` | 自动激活yolov11环境 | ✅ 自动处理 |
| `start_system.bat` | 尝试激活环境 | ⚠️ 基础处理 |
| `run.py` | Python启动脚本 | ⚠️ 检测并提醒 |
| `quick_start.py` | 快速启动 | ⚠️ 检测并提醒 |

## 💡 最佳实践

1. **推荐使用** `start_conda.bat` 进行启动
2. **手动启动前** 确保激活yolov11环境
3. **遇到问题时** 先检查环境状态
4. **安装新包时** 确保在正确环境中

## 🔗 相关命令

```bash
# 环境管理
conda activate yolov11      # 激活环境
conda deactivate           # 退出环境
conda env list            # 列出所有环境

# 系统检查
python system_status.py   # 检查系统状态
python fix_dependencies.py # 修复依赖

# 启动应用
python run.py             # 完整启动流程
python quick_start.py     # 快速启动Web应用
```

记住：始终在yolov11环境中运行江豚检测系统！🐋 