#!/usr/bin/env python3
"""
铜陵无人机数据检测启动脚本 - 直接运行最快版本
"""

import os
import sys
import subprocess
import time
from pathlib import Path

# 导入模块化终极优化检测器
from tongling_detection_modular import TonglingModularOptimizedDetector

def check_dependencies():
    """检查依赖"""
    required_packages = [
        'ultralytics',
        'sahi',
        'opencv-python',
        'pandas',
        'numpy',
        'matplotlib',
        'seaborn',
        'folium',
        'exifread',
        'psutil',
        'tqdm' # tqdm也被用到了
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            if package == 'opencv-python':
                import cv2
            elif package == 'tqdm':
                import tqdm
            else:
                __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    return True

def check_files():
    """检查必要文件"""
    # 检查模型文件
    model_path = "best.engine"
    if not os.path.exists(model_path):
        print(f"❌ TensorRT模型文件不存在: {model_path}")
        return False
    print(f"✅ 找到模型文件: {model_path}")
    
    # 检查数据文件夹
    data_path = "20250718铜陵"
    if not os.path.exists(data_path):
        print(f"❌ 数据文件夹不存在: {data_path}")
        return False
    print(f"✅ 找到数据文件夹: {data_path}")
    return True

def run_fastest_detection():
    """直接运行最快的检测器"""
    print("\n" + "="*60)
    print("🐋 铜陵无人机数据检测系统 - 启动最快版本")
    print("="*60)
    
    print("\n1. 检查依赖...")
    if not check_dependencies():
        return
    
    print("\n2. 检查文件...")
    if not check_files():
        return
    
    print("\n3. 启动模块化终极优化检测...")
    print("="*60)
    
    try:
        model_path = "best.engine"
        data_path = "20250718铜陵"
        
        detector_app = TonglingModularOptimizedDetector(
            model_path=model_path,
            confidence_threshold=0.5,
            slice_size=1280,
            data_loader_max_workers=64, # CPU核心数
            max_memory_gb=200 
        )
        
        # 限制只处理前两条航线
        detector_app.process_all_folders(data_path, limit_folders=2)
        
        print("\n🎉 模块化终极优化检测完成！结果保存在 tongling_results_modular_ultimate_optimized 文件夹中")
        show_results()
        
    except Exception as e:
        print(f"\n❌ 运行检测时出错: {e}")
        import traceback
        traceback.print_exc()
        print("\n" + "="*60)
        print("❌ 检测任务失败！")
        print("请检查错误信息并重试")
        print("="*60)

def show_results():
    """显示结果"""
    print("\n" + "="*60)
    print("📊 检测结果")
    print("="*60)
    
    result_dirs = ["tongling_results_modular_ultimate_optimized"]
    
    for result_dir in result_dirs:
        if os.path.exists(result_dir):
            print(f"\n📁 结果文件夹: {result_dir}")
            result_path = Path(result_dir)
            files = list(result_path.rglob("*"))
            
            reports = [f for f in files if f.suffix == '.md']
            csv_files = [f for f in files if f.suffix == '.csv']
            json_files = [f for f in files if f.suffix == '.json']
            html_files = [f for f in files if f.suffix == '.html']
            png_files = [f for f in files if f.suffix == '.png']
            
            print(f"   📄 报告文件: {len(reports)} 个")
            print(f"   📊 CSV文件: {len(csv_files)} 个")
            print(f"   📋 JSON文件: {len(json_files)} 个")
            print(f"   🗺️ HTML地图: {len(html_files)} 个")
            print(f"   📈 图表文件: {len(png_files)} 个")
            
            main_report_found = False
            for r in reports:
                if "总体检测报告_模块化终极优化版本.md" in r.name:
                    print(f"   📋 总体报告: {r}")
                    main_report_found = True
                    break
            if not main_report_found:
                print("   📋 总体报告: 未找到")

            map_files = [f for f in html_files if "地图" in f.name]
            for map_file in map_files:
                print(f"   🗺️ 地图文件: {map_file}")
        else:
            print(f"❌ 结果文件夹不存在: {result_dir}")

def main():
    run_fastest_detection()

if __name__ == "__main__":
    main()