#!/usr/bin/env python3
"""
铜陵无人机数据检测脚本 - TensorRT版本
支持TensorRT模型(best.engine)进行高效推理
"""

import os
import sys
import cv2
import json
import csv
import logging
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Tuple
import exifread
import re
import torch
import tensorrt as trt
import pycuda.driver as cuda
import pycuda.autoinit
from collections import defaultdict
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings
import matplotlib.pyplot as plt
import seaborn as sns
import folium
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class TensorRTDetector:
    """TensorRT模型检测器"""
    
    def __init__(self, engine_path, input_shape=(1, 3, 640, 640)):
        self.engine_path = engine_path
        self.input_shape = input_shape
        self.logger = logging.getLogger('TensorRTDetector')
        
        # 初始化TensorRT
        self.logger = trt.Logger(trt.Logger.WARNING)
        self.runtime = trt.Runtime(self.logger)
        
        # 加载引擎
        self.load_engine()
        
        # 创建执行上下文
        self.context = self.engine.create_execution_context()
        
        # 分配GPU内存
        self.allocate_buffers()
        
    def load_engine(self):
        """加载TensorRT引擎"""
        try:
            with open(self.engine_path, 'rb') as f:
                engine_data = f.read()
            self.engine = self.runtime.deserialize_cuda_engine(engine_data)
            self.logger.info(f"TensorRT引擎加载成功: {self.engine_path}")
        except Exception as e:
            self.logger.error(f"TensorRT引擎加载失败: {e}")
            raise
            
    def allocate_buffers(self):
        """分配GPU内存缓冲区"""
        self.inputs = []
        self.outputs = []
        self.bindings = []
        
        for binding in self.engine:
            size = trt.volume(self.engine.get_binding_shape(binding)) * self.engine.max_batch_size
            dtype = trt.nptype(self.engine.get_binding_dtype(binding))
            
            # 分配GPU内存
            gpu_mem = cuda.mem_alloc(size * dtype.itemsize)
            self.bindings.append(int(gpu_mem))
            
            if self.engine.binding_is_input(binding):
                self.inputs.append({'gpu_mem': gpu_mem, 'size': size, 'dtype': dtype})
            else:
                self.outputs.append({'gpu_mem': gpu_mem, 'size': size, 'dtype': dtype})
                
    def preprocess_image(self, image_path):
        """预处理图像"""
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
            
        # 调整大小
        image = cv2.resize(image, (self.input_shape[3], self.input_shape[2]))
        
        # 归一化
        image = image.astype(np.float32) / 255.0
        
        # 转换为CHW格式
        image = np.transpose(image, (2, 0, 1))
        
        # 添加batch维度
        image = np.expand_dims(image, axis=0)
        
        return image
        
    def detect(self, image_path, confidence_threshold=0.5):
        """执行检测"""
        try:
            # 预处理图像
            input_data = self.preprocess_image(image_path)
            
            # 复制数据到GPU
            cuda.memcpy_htod(self.inputs[0]['gpu_mem'], input_data.astype(np.float32))
            
            # 执行推理
            self.context.execute_v2(bindings=self.bindings)
            
            # 获取输出
            output = np.empty(self.outputs[0]['size'], dtype=self.outputs[0]['dtype'])
            cuda.memcpy_dtoh(output, self.outputs[0]['gpu_mem'])
            
            # 解析输出
            detections = self.parse_output(output, confidence_threshold)
            
            return detections
            
        except Exception as e:
            self.logger.error(f"检测失败: {e}")
            return []
            
    def parse_output(self, output, confidence_threshold):
        """解析TensorRT输出"""
        # 假设输出格式为 [batch, num_detections, 7] (x1, y1, x2, y2, confidence, class_id, class_score)
        detections = []
        
        # 重塑输出
        output = output.reshape(-1, 7)
        
        for detection in output:
            x1, y1, x2, y2, confidence, class_id, class_score = detection
            
            if confidence >= confidence_threshold:
                detections.append({
                    'bbox_x1': float(x1),
                    'bbox_y1': float(y1),
                    'bbox_x2': float(x2),
                    'bbox_y2': float(y2),
                    'confidence': float(confidence),
                    'class_id': int(class_id),
                    'class_name': 'finless_porpoise'
                })
                
        return detections

class TonglingTensorRTDetector:
    def __init__(self, model_path="best.engine", batch_size=200, confidence_threshold=0.5):
        """
        初始化TensorRT检测器
        
        Args:
            model_path: TensorRT模型路径
            batch_size: 批处理大小
            confidence_threshold: 置信度阈值
        """
        self.model_path = model_path
        self.batch_size = batch_size
        self.confidence_threshold = confidence_threshold
        self.results_dir = Path("tongling_results_tensorrt")
        self.results_dir.mkdir(exist_ok=True)
        
        # 初始化日志
        self.setup_logging()
        
        # 加载TensorRT模型
        self.load_model()
        
        # 检测结果存储
        self.all_detections = []
        self.tracking_results = {}
        
    def setup_logging(self):
        """设置日志"""
        log_file = self.results_dir / f"detection_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('TonglingTensorRTDetector')
        
    def load_model(self):
        """加载TensorRT模型"""
        try:
            self.logger.info(f"正在加载TensorRT模型: {self.model_path}")
            self.detector = TensorRTDetector(self.model_path)
            self.logger.info("TensorRT模型加载成功")
        except Exception as e:
            self.logger.error(f"TensorRT模型加载失败: {e}")
            raise
            
    def extract_gps_from_exif(self, image_path: str) -> Dict[str, float]:
        """从图片EXIF信息中提取GPS坐标"""
        try:
            with open(image_path, 'rb') as f:
                tags = exifread.process_file(f)
            
            gps_info = {}
            
            # 提取GPS坐标
            if 'GPS GPSLatitude' in tags and 'GPS GPSLongitude' in tags:
                lat = self._convert_to_degrees(tags['GPS GPSLatitude'].values)
                lon = self._convert_to_degrees(tags['GPS GPSLongitude'].values)
                
                # 处理南北半球
                if 'GPS GPSLatitudeRef' in tags:
                    if tags['GPS GPSLatitudeRef'].values == 'S':
                        lat = -lat
                        
                # 处理东西半球
                if 'GPS GPSLongitudeRef' in tags:
                    if tags['GPS GPSLongitudeRef'].values == 'W':
                        lon = -lon
                        
                gps_info['latitude'] = lat
                gps_info['longitude'] = lon
                
            # 提取高度信息
            if 'GPS GPSAltitude' in tags:
                altitude = float(tags['GPS GPSAltitude'].values[0])
                gps_info['altitude'] = altitude
                
            # 提取时间信息
            if 'EXIF DateTimeOriginal' in tags:
                gps_info['timestamp'] = str(tags['EXIF DateTimeOriginal'].values)
                
            return gps_info
            
        except Exception as e:
            self.logger.warning(f"提取GPS信息失败 {image_path}: {e}")
            return {}
            
    def _convert_to_degrees(self, values):
        """将GPS坐标转换为度"""
        d = float(values[0].num) / float(values[0].den)
        m = float(values[1].num) / float(values[1].den)
        s = float(values[2].num) / float(values[2].den)
        return d + (m / 60.0) + (s / 3600.0)
        
    def extract_gps_from_srt(self, srt_path: str) -> Dict[str, List[Dict]]:
        """从SRT字幕文件中提取GPS信息"""
        gps_data = []
        try:
            with open(srt_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 解析SRT文件
            blocks = content.strip().split('\n\n')
            for block in blocks:
                lines = block.strip().split('\n')
                if len(lines) >= 3:
                    # 提取时间戳
                    time_line = lines[1]
                    time_match = re.search(r'(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})', time_line)
                    
                    if time_match:
                        start_time = time_match.group(1)
                        end_time = time_match.group(2)
                        
                        # 提取GPS信息
                        gps_info = {}
                        for line in lines[2:]:
                            if 'GPS' in line or 'latitude' in line.lower() or 'longitude' in line.lower():
                                # 提取经纬度
                                lat_match = re.search(r'lat[itude]*[:\s]*([-\d.]+)', line, re.IGNORECASE)
                                lon_match = re.search(r'lon[gitude]*[:\s]*([-\d.]+)', line, re.IGNORECASE)
                                alt_match = re.search(r'alt[itude]*[:\s]*([-\d.]+)', line, re.IGNORECASE)
                                
                                if lat_match:
                                    gps_info['latitude'] = float(lat_match.group(1))
                                if lon_match:
                                    gps_info['longitude'] = float(lon_match.group(1))
                                if alt_match:
                                    gps_info['altitude'] = float(alt_match.group(1))
                                    
                        if gps_info:
                            gps_info['start_time'] = start_time
                            gps_info['end_time'] = end_time
                            gps_data.append(gps_info)
                            
        except Exception as e:
            self.logger.warning(f"提取SRT GPS信息失败 {srt_path}: {e}")
            
        return gps_data
        
    def detect_image(self, image_path: str, gps_info: Dict = None) -> List[Dict]:
        """检测图片"""
        try:
            # 使用TensorRT进行检测
            detections = self.detector.detect(image_path, self.confidence_threshold)
            
            # 添加GPS信息
            for detection in detections:
                detection['detection_method'] = 'TensorRT'
                detection['image_path'] = image_path
                if gps_info:
                    detection.update(gps_info)
                    
            return detections
            
        except Exception as e:
            self.logger.error(f"图片检测失败 {image_path}: {e}")
            return []
            
    def detect_video(self, video_path: str, srt_path: str = None) -> List[Dict]:
        """检测视频文件"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                self.logger.error(f"无法打开视频: {video_path}")
                return []
                
            # 获取视频信息
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps
            
            self.logger.info(f"视频信息: {video_path}")
            self.logger.info(f"FPS: {fps}, 总帧数: {total_frames}, 时长: {duration:.2f}秒")
            
            # 提取GPS信息
            gps_data = []
            if srt_path and os.path.exists(srt_path):
                gps_data = self.extract_gps_from_srt(srt_path)
                self.logger.info(f"从SRT文件提取到 {len(gps_data)} 条GPS记录")
            
            # 每3秒提取一帧进行检测
            frame_interval = int(fps * 3)
            detections = []
            frame_count = 0
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                    
                # 每3秒处理一帧
                if frame_count % frame_interval == 0:
                    # 保存临时帧
                    temp_frame_path = f"temp_frame_{frame_count}.jpg"
                    cv2.imwrite(temp_frame_path, frame)
                    
                    # 获取当前时间对应的GPS信息
                    current_time = frame_count / fps
                    current_gps = self._get_gps_at_time(gps_data, current_time)
                    
                    # 检测当前帧
                    frame_detections = self.detect_image(temp_frame_path, current_gps)
                    
                    # 添加时间信息
                    for detection in frame_detections:
                        detection['frame_time'] = current_time
                        detection['frame_number'] = frame_count
                        
                    detections.extend(frame_detections)
                    
                    # 删除临时文件
                    os.remove(temp_frame_path)
                    
                frame_count += 1
                
            cap.release()
            return detections
            
        except Exception as e:
            self.logger.error(f"视频检测失败 {video_path}: {e}")
            return []
            
    def _get_gps_at_time(self, gps_data: List[Dict], current_time: float) -> Dict:
        """根据时间获取对应的GPS信息"""
        if not gps_data:
            return {}
            
        # 找到最接近当前时间的GPS记录
        for gps_record in gps_data:
            start_time = self._parse_srt_time(gps_record.get('start_time', '00:00:00,000'))
            end_time = self._parse_srt_time(gps_record.get('end_time', '00:00:00,000'))
            
            if start_time <= current_time <= end_time:
                return gps_record
                
        return {}
        
    def _parse_srt_time(self, time_str: str) -> float:
        """解析SRT时间格式"""
        try:
            time_parts = time_str.replace(',', '.').split(':')
            hours = int(time_parts[0])
            minutes = int(time_parts[1])
            seconds = float(time_parts[2])
            return hours * 3600 + minutes * 60 + seconds
        except:
            return 0.0
            
    def process_folder(self, folder_path: str):
        """处理整个文件夹"""
        folder_path = Path(folder_path)
        self.logger.info(f"开始处理文件夹: {folder_path}")
        
        # 创建结果子目录
        folder_results_dir = self.results_dir / folder_path.name
        folder_results_dir.mkdir(exist_ok=True)
        
        # 收集所有文件
        image_files = []
        video_files = []
        
        for file_path in folder_path.rglob("*"):
            if file_path.is_file():
                ext = file_path.suffix.lower()
                if ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                    image_files.append(file_path)
                elif ext in ['.mp4', '.avi', '.mov', '.mkv']:
                    video_files.append(file_path)
                    
        self.logger.info(f"找到 {len(image_files)} 个图片文件和 {len(video_files)} 个视频文件")
        
        # 处理图片文件
        image_detections = []
        for image_file in image_files:
            self.logger.info(f"处理图片: {image_file}")
            gps_info = self.extract_gps_from_exif(str(image_file))
            detections = self.detect_image(str(image_file), gps_info)
            image_detections.extend(detections)
            
        # 处理视频文件
        video_detections = []
        for video_file in video_files:
            self.logger.info(f"处理视频: {video_file}")
            # 查找对应的SRT文件
            srt_file = video_file.with_suffix('.srt')
            if not srt_file.exists():
                srt_file = video_file.with_suffix('.SRT')
                
            detections = self.detect_video(str(video_file), str(srt_file) if srt_file.exists() else None)
            video_detections.extend(detections)
            
        # 合并所有检测结果
        all_detections = image_detections + video_detections
        
        # 保存结果
        self._save_results(all_detections, folder_results_dir, folder_path.name)
        
        return all_detections
        
    def _save_results(self, detections: List[Dict], results_dir: Path, folder_name: str):
        """保存检测结果"""
        # 保存CSV文件
        csv_file = results_dir / f"{folder_name}_detections.csv"
        if detections:
            df = pd.DataFrame(detections)
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            self.logger.info(f"检测结果已保存到: {csv_file}")
            
        # 保存JSON文件
        json_file = results_dir / f"{folder_name}_detections.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(detections, f, ensure_ascii=False, indent=2)
            
        # 生成统计报告
        self._generate_report(detections, results_dir, folder_name)
        
    def _generate_report(self, detections: List[Dict], results_dir: Path, folder_name: str):
        """生成检测报告"""
        if not detections:
            return
            
        # 统计信息
        total_detections = len(detections)
        confidence_scores = [d['confidence'] for d in detections]
        avg_confidence = np.mean(confidence_scores)
        max_confidence = np.max(confidence_scores)
        min_confidence = np.min(confidence_scores)
        
        # 生成报告
        report = f"""
# 铜陵无人机检测报告 - {folder_name} (TensorRT)

## 检测统计
- 总检测数量: {total_detections}
- 平均置信度: {avg_confidence:.3f}
- 最高置信度: {max_confidence:.3f}
- 最低置信度: {min_confidence:.3f}

## 检测方法
- TensorRT推理引擎

## 性能信息
- 模型: best.engine (TensorRT)
- 推理框架: TensorRT
- 硬件加速: GPU
"""
        
        # 保存报告
        report_file = results_dir / f"{folder_name}_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
            
        # 生成可视化图表
        self._generate_visualizations(detections, results_dir, folder_name)
        
    def _generate_visualizations(self, detections: List[Dict], results_dir: Path, folder_name: str):
        """生成可视化图表"""
        if not detections:
            return
            
        # 置信度分布图
        plt.figure(figsize=(10, 6))
        confidence_scores = [d['confidence'] for d in detections]
        plt.hist(confidence_scores, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        plt.xlabel('置信度')
        plt.ylabel('检测数量')
        plt.title(f'{folder_name} - TensorRT检测置信度分布')
        plt.grid(True, alpha=0.3)
        plt.savefig(results_dir / f"{folder_name}_confidence_distribution.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        # GPS分布图（如果有GPS信息）
        gps_detections = [d for d in detections if 'latitude' in d and 'longitude' in d]
        if gps_detections:
            plt.figure(figsize=(12, 8))
            lats = [d['latitude'] for d in gps_detections]
            lons = [d['longitude'] for d in gps_detections]
            confidences = [d['confidence'] for d in gps_detections]
            
            scatter = plt.scatter(lons, lats, c=confidences, cmap='viridis', s=50, alpha=0.7)
            plt.colorbar(scatter, label='置信度')
            plt.xlabel('经度')
            plt.ylabel('纬度')
            plt.title(f'{folder_name} - TensorRT检测目标GPS分布')
            plt.grid(True, alpha=0.3)
            plt.savefig(results_dir / f"{folder_name}_gps_distribution.png", dpi=300, bbox_inches='tight')
            plt.close()
            
    def process_all_folders(self, base_path: str):
        """处理所有子文件夹"""
        base_path = Path(base_path)
        self.logger.info(f"开始处理所有文件夹: {base_path}")
        
        # 获取所有子文件夹
        subfolders = [f for f in base_path.iterdir() if f.is_dir()]
        self.logger.info(f"找到 {len(subfolders)} 个子文件夹")
        
        all_results = {}
        
        # 使用多线程处理
        with ThreadPoolExecutor(max_workers=4) as executor:
            future_to_folder = {executor.submit(self.process_folder, str(folder)): folder for folder in subfolders}
            
            for future in as_completed(future_to_folder):
                folder = future_to_folder[future]
                try:
                    detections = future.result()
                    all_results[folder.name] = detections
                    self.logger.info(f"完成处理: {folder.name}")
                except Exception as e:
                    self.logger.error(f"处理失败 {folder.name}: {e}")
                    
        # 生成总体报告
        self._generate_summary_report(all_results)
        
    def _generate_summary_report(self, all_results: Dict[str, List[Dict]]):
        """生成总体报告"""
        summary_file = self.results_dir / "总体检测报告_TensorRT.md"
        
        total_detections = sum(len(detections) for detections in all_results.values())
        total_folders = len(all_results)
        
        summary = f"""
# 铜陵无人机检测总体报告 - TensorRT

## 检测统计
- 总文件夹数: {total_folders}
- 总检测数量: {total_detections}
- 推理引擎: TensorRT
- 模型文件: best.engine

## 各文件夹检测结果
"""
        
        for folder_name, detections in all_results.items():
            if detections:
                avg_confidence = np.mean([d['confidence'] for d in detections])
                summary += f"- {folder_name}: {len(detections)} 个检测，平均置信度 {avg_confidence:.3f}\n"
            else:
                summary += f"- {folder_name}: 无检测结果\n"
                
        # 保存总体报告
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary)
            
        self.logger.info(f"总体报告已保存到: {summary_file}")

def main():
    """主函数"""
    print("🐋 铜陵无人机数据检测系统 - TensorRT版本")
    print("="*50)
    
    # 检查模型文件
    model_path = "best.engine"
    if not os.path.exists(model_path):
        print(f"❌ TensorRT模型文件不存在: {model_path}")
        return
        
    # 检查数据文件夹
    data_path = "20250718铜陵"
    if not os.path.exists(data_path):
        print(f"❌ 数据文件夹不存在: {data_path}")
        return
        
    # 初始化检测器
    detector = TonglingTensorRTDetector(
        model_path=model_path,
        batch_size=200,
        confidence_threshold=0.5
    )
    
    # 处理所有文件夹
    detector.process_all_folders(data_path)
    
    print("🎉 TensorRT检测完成！结果保存在 tongling_results_tensorrt 文件夹中")

if __name__ == "__main__":
    main() 