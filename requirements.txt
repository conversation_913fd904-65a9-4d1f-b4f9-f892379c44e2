# 核心深度学习框架
torch>=2.0.0
torchvision>=0.15.0
ultralytics>=8.0.0

# 图像处理
opencv-python>=4.8.0
Pillow>=10.0.0
numpy>=1.24.0

# EXIF信息处理
exifread>=3.0.0
piexif>=1.1.3

# 数据处理
pandas>=2.0.0
scikit-learn>=1.3.0

# SAHI切片检测
sahi>=0.11.14

# 可视化
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0

# 视频处理
moviepy>=1.0.3

# 进度条
tqdm>=4.65.0

# 配置文件
pyyaml>=6.0

# 数学计算
geopy>=2.3.0
utm>=0.7.0

# 注意: pathlib, argparse, logging, datetime, json 是Python内置模块，无需安装 