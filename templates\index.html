<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐋 江豚检测系统 - Web版本</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 40px;
        }
        
        .upload-section {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }
        
        .upload-section:hover {
            border-color: #4CAF50;
            background: #f0fff0;
        }
        
        .upload-section.dragover {
            border-color: #4CAF50;
            background: #e8f5e8;
        }
        
        .file-input {
            display: none;
        }
        
        .upload-btn {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .upload-btn:hover {
            background: #45a049;
        }
        
        .settings-panel {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .settings-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        
        .settings-row label {
            font-weight: bold;
            color: #333;
        }
        
        .checkbox-container {
            display: flex;
            align-items: center;
        }
        
        .checkbox-container input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
        }
        
        .slider-container {
            display: flex;
            align-items: center;
        }
        
        .slider {
            margin: 0 15px;
            width: 200px;
        }
        
        .confidence-value {
            font-weight: bold;
            color: #4CAF50;
            min-width: 50px;
        }
        
        .detect-btn {
            background: #ff6b6b;
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 8px;
            font-size: 1.2em;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
            transition: background 0.3s ease;
        }
        
        .detect-btn:hover {
            background: #ff5252;
        }
        
        .detect-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .results-section {
            margin-top: 30px;
            display: none;
        }
        
        .result-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .result-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .result-content {
            padding: 20px;
        }
        
        .result-image {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .result-image img {
            max-width: 100%;
            max-height: 400px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .detection-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 6px;
            border-left: 4px solid #4CAF50;
        }
        
        .info-label {
            font-weight: bold;
            color: #666;
            font-size: 0.9em;
        }
        
        .info-value {
            color: #333;
            font-size: 1.1em;
            margin-top: 4px;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .summary-stats {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .download-section {
            text-align: center;
            margin-top: 20px;
        }
        
        .download-btn {
            background: #2196F3;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            display: inline-block;
            margin: 0 10px;
            transition: background 0.3s ease;
        }
        
        .download-btn:hover {
            background: #1976D2;
        }
        
        .file-list {
            max-height: 200px;
            overflow-y: auto;
            background: #f8f9fa;
            border-radius: 6px;
            padding: 10px;
            margin-top: 15px;
        }
        
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .file-item:last-child {
            border-bottom: none;
        }
        
        .status-indicator {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .system-status {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .system-status.ready {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        
        .system-status.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐋 江豚检测系统</h1>
            <p>基于YOLOv8+SAHI技术的航拍江豚检测与GPS定位系统 - Web版本</p>
        </div>
        
        <div class="main-content">
            <!-- 系统状态 -->
            <div class="system-status" id="systemStatus">
                <span id="statusText">🔄 正在检查系统状态...</span>
            </div>
            
            <!-- 文件上传区域 -->
            <div class="upload-section" id="uploadSection">
                <h3>📁 选择要检测的图片</h3>
                <p>支持格式：JPG, PNG, BMP, TIFF 等</p>
                <p>支持批量上传，最大文件大小：100MB</p>
                <input type="file" id="fileInput" class="file-input" multiple accept="image/*">
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    📂 选择文件
                </button>
                <div class="file-list" id="fileList" style="display: none;"></div>
            </div>
            
            <!-- 检测参数设置 -->
            <div class="settings-panel">
                <h3>⚙️ 检测参数设置</h3>
                
                <div class="settings-row">
                    <label>启用SAHI切片检测</label>
                    <div class="checkbox-container">
                        <input type="checkbox" id="useSahi" checked>
                        <span>提升小目标检测精度</span>
                    </div>
                </div>
                
                <div class="settings-row">
                    <label>置信度阈值</label>
                    <div class="slider-container">
                        <span>0.1</span>
                        <input type="range" id="confidenceSlider" class="slider" min="0.1" max="0.9" step="0.05" value="0.25">
                        <span>0.9</span>
                        <span class="confidence-value" id="confidenceValue">0.25</span>
                    </div>
                </div>
                
                <button class="detect-btn" id="detectBtn" onclick="startDetection()">
                    🔍 开始检测江豚
                </button>
            </div>
            
            <!-- 加载状态 -->
            <div class="loading" id="loadingSection">
                <div class="spinner"></div>
                <h3>🔍 正在检测江豚...</h3>
                <p>请稍等，系统正在分析您的图片</p>
            </div>
            
            <!-- 检测结果 -->
            <div class="results-section" id="resultsSection">
                <!-- 结果统计 -->
                <div class="summary-stats" id="summaryStats" style="display: none;">
                    <h3>📊 检测结果统计</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-number" id="totalImages">0</span>
                            <span class="stat-label">处理图片</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="totalDetections">0</span>
                            <span class="stat-label">检测到江豚</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="imagesWithDetections">0</span>
                            <span class="stat-label">有检测结果</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="avgConfidence">0.00</span>
                            <span class="stat-label">平均置信度</span>
                        </div>
                    </div>
                    
                    <div class="download-section">
                        <a href="#" class="download-btn" id="downloadReportBtn" style="display: none;">
                            📊 下载检测报告 (CSV)
                        </a>
                    </div>
                </div>
                
                <!-- 详细结果 -->
                <div id="detailedResults"></div>
            </div>
        </div>
    </div>

    <script>
        let selectedFiles = [];
        let currentSessionId = null;
        
        // 页面加载时检查系统状态
        window.addEventListener('load', function() {
            checkSystemStatus();
        });
        
        // 文件选择处理
        document.getElementById('fileInput').addEventListener('change', function(e) {
            selectedFiles = Array.from(e.target.files);
            updateFileList();
        });
        
        // 置信度滑块
        document.getElementById('confidenceSlider').addEventListener('input', function(e) {
            document.getElementById('confidenceValue').textContent = e.target.value;
        });
        
        // 拖拽上传
        const uploadSection = document.getElementById('uploadSection');
        
        uploadSection.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        });
        
        uploadSection.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
        });
        
        uploadSection.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            selectedFiles = Array.from(e.dataTransfer.files);
            updateFileList();
        });
        
        function checkSystemStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    const statusDiv = document.getElementById('systemStatus');
                    const statusText = document.getElementById('statusText');
                    
                    if (data.detector_ready) {
                        statusDiv.className = 'system-status ready';
                        statusText.innerHTML = '✅ 系统就绪 - 江豚检测器已加载';
                        if (data.model_info && data.model_info.classes) {
                            statusText.innerHTML += ` (检测类别: ${Object.values(data.model_info.classes).join(', ')})`;
                        }
                    } else {
                        statusDiv.className = 'system-status error';
                        statusText.innerHTML = '❌ 系统未就绪 - 请检查模型文件';
                    }
                })
                .catch(error => {
                    const statusDiv = document.getElementById('systemStatus');
                    const statusText = document.getElementById('statusText');
                    statusDiv.className = 'system-status error';
                    statusText.innerHTML = '❌ 无法连接到服务器';
                });
        }
        
        function updateFileList() {
            const fileList = document.getElementById('fileList');
            if (selectedFiles.length > 0) {
                fileList.style.display = 'block';
                fileList.innerHTML = selectedFiles.map(file => 
                    `<div class="file-item">
                        <span>${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)</span>
                        <span class="status-indicator">待检测</span>
                    </div>`
                ).join('');
            } else {
                fileList.style.display = 'none';
            }
        }
        
        function startDetection() {
            if (selectedFiles.length === 0) {
                alert('请先选择要检测的图片文件！');
                return;
            }
            
            const formData = new FormData();
            selectedFiles.forEach(file => {
                formData.append('files', file);
            });
            
            formData.append('use_sahi', document.getElementById('useSahi').checked);
            formData.append('confidence', document.getElementById('confidenceSlider').value);
            
            // 显示加载状态
            document.getElementById('loadingSection').style.display = 'block';
            document.getElementById('detectBtn').disabled = true;
            document.getElementById('resultsSection').style.display = 'none';
            
            // 发送检测请求
            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loadingSection').style.display = 'none';
                document.getElementById('detectBtn').disabled = false;
                
                if (data.error) {
                    alert('检测失败：' + data.error);
                    return;
                }
                
                currentSessionId = data.session_id;
                displayResults(data);
            })
            .catch(error => {
                document.getElementById('loadingSection').style.display = 'none';
                document.getElementById('detectBtn').disabled = false;
                alert('检测失败：' + error.message);
            });
        }
        
        function displayResults(data) {
            document.getElementById('resultsSection').style.display = 'block';
            
            // 显示统计信息
            const summaryStats = document.getElementById('summaryStats');
            summaryStats.style.display = 'block';
            
            const totalImages = data.results.length;
            const totalDetections = data.total_detections;
            const imagesWithDetections = data.results.filter(r => r.detection_count > 0).length;
            const avgConfidence = data.results
                .filter(r => r.detections && r.detections.length > 0)
                .flatMap(r => r.detections)
                .reduce((sum, d, _, arr) => arr.length > 0 ? sum + d.confidence / arr.length : 0, 0) || 0;
            
            document.getElementById('totalImages').textContent = totalImages;
            document.getElementById('totalDetections').textContent = totalDetections;
            document.getElementById('imagesWithDetections').textContent = imagesWithDetections;
            document.getElementById('avgConfidence').textContent = avgConfidence.toFixed(3);
            
            // 显示下载按钮
            if (currentSessionId) {
                const downloadBtn = document.getElementById('downloadReportBtn');
                downloadBtn.style.display = 'inline-block';
                downloadBtn.href = `/download_report/${currentSessionId}`;
            }
            
            // 显示详细结果
            const detailedResults = document.getElementById('detailedResults');
            detailedResults.innerHTML = data.results.map(result => 
                createResultCard(result)
            ).join('');
        }
        
        function createResultCard(result) {
            const hasDetections = result.detections && result.detections.length > 0;
            const detectionCount = result.detection_count || 0;
            
            return `
                <div class="result-card">
                    <div class="result-header">
                        <h4>📄 ${result.filename}</h4>
                        <span class="status-indicator ${hasDetections ? 'status-success' : 'status-error'}">
                            ${hasDetections ? `发现 ${detectionCount} 个江豚` : '未发现江豚'}
                        </span>
                    </div>
                    <div class="result-content">
                        ${result.result_image ? `
                            <div class="result-image">
                                <img src="${result.result_image}" alt="检测结果" onclick="window.open('${result.result_image}', '_blank')">
                                <p><small>点击图片查看大图</small></p>
                            </div>
                        ` : ''}
                        
                        ${hasDetections ? 
                            result.detections.map((detection, index) => `
                                <div class="detection-info">
                                    <div class="info-item">
                                        <div class="info-label">检测 #${index + 1}</div>
                                        <div class="info-value">江豚目标</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">置信度</div>
                                        <div class="info-value">${(detection.confidence * 100).toFixed(1)}%</div>
                                    </div>
                                    ${detection.latitude ? `
                                        <div class="info-item">
                                            <div class="info-label">GPS纬度</div>
                                            <div class="info-value">${detection.latitude}</div>
                                        </div>
                                        <div class="info-item">
                                            <div class="info-label">GPS经度</div>
                                            <div class="info-value">${detection.longitude}</div>
                                        </div>
                                    ` : ''}
                                    <div class="info-item">
                                        <div class="info-label">位置坐标</div>
                                        <div class="info-value">X:${detection.x || 'N/A'}, Y:${detection.y || 'N/A'}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">目标尺寸</div>
                                        <div class="info-value">${detection.width || 'N/A'} × ${detection.height || 'N/A'}</div>
                                    </div>
                                </div>
                                ${index < result.detections.length - 1 ? '<hr style="margin: 15px 0;">' : ''}
                            `).join('') :
                            '<p style="text-align: center; color: #666; padding: 20px;">该图片中未检测到江豚目标</p>'
                        }
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html> 