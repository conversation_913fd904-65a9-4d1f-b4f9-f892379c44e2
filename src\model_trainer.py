"""
模型训练模块
支持YOLOv11模型的训练、验证、权重迁移和参数调优
"""

import os
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging
import json
from datetime import datetime
import shutil

try:
    from ultralytics import YOLO
    ULTRALYTICS_AVAILABLE = True
except ImportError:
    ULTRALYTICS_AVAILABLE = False
    print("警告: ultralytics未安装，请运行: pip install ultralytics")

try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    PLOT_AVAILABLE = True
except ImportError:
    PLOT_AVAILABLE = False
    print("警告: 绘图库未安装，请运行: pip install matplotlib seaborn")


class FinlessPorpoiseTrainer:
    """江豚检测模型训练器"""
    
    def __init__(self, config_path: str = "training_config.yaml"):
        """
        初始化训练器
        
        Args:
            config_path: 训练配置文件路径
        """
        self.config = self._load_training_config(config_path)
        self.logger = self._setup_logger()
        
        # 训练历史
        self.training_history = []
        
        # 检查ultralytics可用性
        if not ULTRALYTICS_AVAILABLE:
            self.logger.error("ultralytics未安装，无法进行模型训练")
            return
    
    def _load_training_config(self, config_path: str) -> Dict:
        """加载训练配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.warning(f"无法加载训练配置文件 {config_path}, 使用默认配置: {e}")
            return self._get_default_training_config()
    
    def _get_default_training_config(self) -> Dict:
        """获取默认训练配置"""
        return {
            'model': {
                'base_model': 'yolo11n.pt',  # 基础模型
                'input_size': 1280,  # 输入尺寸
                'classes': ['finless_porpoise']  # 类别列表
            },
            'dataset': {
                'train_dir': 'datasets/train',
                'val_dir': 'datasets/val',
                'test_dir': 'datasets/test',
                'yaml_path': 'datasets/dataset.yaml'
            },
            'training': {
                'epochs': 100,
                'batch_size': 16,
                'learning_rate': 0.01,
                'weight_decay': 0.0005,
                'momentum': 0.937,
                'warmup_epochs': 3,
                'patience': 50,  # 早停耐心值
                'save_period': 10,  # 保存周期
                'workers': 8,  # 数据加载器工作进程数
                'device': 'auto'  # 设备选择
            },
            'augmentation': {
                'hsv_h': 0.015,
                'hsv_s': 0.7,
                'hsv_v': 0.4,
                'degrees': 0.0,
                'translate': 0.1,
                'scale': 0.5,
                'shear': 0.0,
                'perspective': 0.0,
                'flipud': 0.0,
                'fliplr': 0.5,
                'mosaic': 1.0,
                'mixup': 0.0,
                'copy_paste': 0.0
            },
            'validation': {
                'val_split': 0.2,  # 验证集比例
                'conf_threshold': 0.001,
                'iou_threshold': 0.6,
                'max_det': 300
            },
            'output': {
                'project_dir': 'runs/train',
                'name': 'finless_porpoise',
                'save_plots': True,
                'save_txt': True,
                'save_conf': True
            }
        }
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('FinlessPorpoiseTrainer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # 控制台输出
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 日志格式
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
            
            # 文件输出
            log_dir = Path("training_logs")
            log_dir.mkdir(exist_ok=True)
            file_handler = logging.FileHandler(
                log_dir / f"training_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log",
                encoding='utf-8'
            )
            file_handler.setLevel(logging.INFO)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def create_dataset_yaml(self, dataset_dir: str, output_path: str = None) -> str:
        """
        创建YOLO格式的数据集配置文件
        
        Args:
            dataset_dir: 数据集根目录
            output_path: 输出YAML文件路径
            
        Returns:
            生成的YAML文件路径
        """
        if output_path is None:
            output_path = Path(dataset_dir) / "dataset.yaml"
        
        dataset_path = Path(dataset_dir).resolve()
        
        # 检查数据集结构
        train_dir = dataset_path / "train"
        val_dir = dataset_path / "val"
        test_dir = dataset_path / "test"
        
        if not train_dir.exists():
            self.logger.error(f"训练集目录不存在: {train_dir}")
            return ""
        
        if not val_dir.exists():
            self.logger.warning(f"验证集目录不存在: {val_dir}")
        
        # 创建数据集配置
        dataset_config = {
            'path': str(dataset_path),
            'train': 'train/images' if (train_dir / 'images').exists() else 'train',
            'val': 'val/images' if (val_dir / 'images').exists() else 'val',
            'names': {0: 'finless_porpoise'},
            'nc': 1  # 类别数
        }
        
        if test_dir.exists():
            dataset_config['test'] = 'test/images' if (test_dir / 'images').exists() else 'test'
        
        # 保存YAML文件
        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(dataset_config, f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"数据集配置文件已创建: {output_path}")
        return str(output_path)
    
    def prepare_training_environment(self, dataset_dir: str) -> bool:
        """
        准备训练环境
        
        Args:
            dataset_dir: 数据集目录
            
        Returns:
            是否准备成功
        """
        try:
            # 创建数据集YAML
            yaml_path = self.create_dataset_yaml(dataset_dir)
            if not yaml_path:
                return False
            
            # 更新配置中的数据集路径
            self.config['dataset']['yaml_path'] = yaml_path
            
            # 创建输出目录
            output_dir = Path(self.config['output']['project_dir'])
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 验证数据集
            self._validate_dataset(dataset_dir)
            
            return True
            
        except Exception as e:
            self.logger.error(f"准备训练环境失败: {e}")
            return False
    
    def _validate_dataset(self, dataset_dir: str):
        """验证数据集结构和内容"""
        dataset_path = Path(dataset_dir)
        
        # 检查训练集
        train_images = list((dataset_path / "train" / "images").glob("*.jpg")) + \
                      list((dataset_path / "train" / "images").glob("*.png"))
        train_labels = list((dataset_path / "train" / "labels").glob("*.txt"))
        
        self.logger.info(f"训练集图片数量: {len(train_images)}")
        self.logger.info(f"训练集标签数量: {len(train_labels)}")
        
        # 检查验证集
        val_images = list((dataset_path / "val" / "images").glob("*.jpg")) + \
                    list((dataset_path / "val" / "images").glob("*.png"))
        val_labels = list((dataset_path / "val" / "labels").glob("*.txt"))
        
        self.logger.info(f"验证集图片数量: {len(val_images)}")
        self.logger.info(f"验证集标签数量: {len(val_labels)}")
        
        if len(train_images) == 0:
            raise ValueError("训练集为空")
        
        if len(val_images) == 0:
            self.logger.warning("验证集为空")
    
    def train_model(self, resume_from: str = None) -> Dict:
        """
        训练模型
        
        Args:
            resume_from: 从检查点恢复训练的路径
            
        Returns:
            训练结果字典
        """
        if not ULTRALYTICS_AVAILABLE:
            self.logger.error("ultralytics未安装，无法训练模型")
            return {}
        
        try:
            # 加载基础模型
            if resume_from and os.path.exists(resume_from):
                model = YOLO(resume_from)
                self.logger.info(f"从检查点恢复训练: {resume_from}")
            else:
                base_model = self.config['model']['base_model']
                model = YOLO(base_model)
                self.logger.info(f"加载基础模型: {base_model}")
            
            # 训练参数
            training_args = {
                'data': self.config['dataset']['yaml_path'],
                'epochs': self.config['training']['epochs'],
                'batch': self.config['training']['batch_size'],
                'imgsz': self.config['model']['input_size'],
                'lr0': self.config['training']['learning_rate'],
                'weight_decay': self.config['training']['weight_decay'],
                'momentum': self.config['training']['momentum'],
                'warmup_epochs': self.config['training']['warmup_epochs'],
                'patience': self.config['training']['patience'],
                'save_period': self.config['training']['save_period'],
                'workers': self.config['training']['workers'],
                'device': self.config['training']['device'],
                'project': self.config['output']['project_dir'],
                'name': self.config['output']['name'],
                'plots': self.config['output']['save_plots'],
                'save_txt': self.config['output']['save_txt'],
                'save_conf': self.config['output']['save_conf'],
                'exist_ok': True
            }
            
            # 添加数据增强参数
            for key, value in self.config['augmentation'].items():
                training_args[key] = value
            
            self.logger.info("开始训练模型...")
            self.logger.info(f"训练参数: {training_args}")
            
            # 开始训练
            results = model.train(**training_args)
            
            # 记录训练结果
            training_result = {
                'training_time': datetime.now().isoformat(),
                'best_weights': str(results.save_dir / 'weights' / 'best.pt'),
                'last_weights': str(results.save_dir / 'weights' / 'last.pt'),
                'results_dir': str(results.save_dir),
                'training_args': training_args
            }
            
            # 保存训练配置
            config_save_path = results.save_dir / 'training_config.yaml'
            with open(config_save_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            
            self.training_history.append(training_result)
            
            self.logger.info(f"训练完成！最佳权重: {training_result['best_weights']}")
            
            return training_result
            
        except Exception as e:
            self.logger.error(f"模型训练失败: {e}")
            return {}
    
    def validate_model(self, weights_path: str, dataset_yaml: str = None) -> Dict:
        """
        验证模型性能
        
        Args:
            weights_path: 模型权重路径
            dataset_yaml: 数据集配置文件路径
            
        Returns:
            验证结果字典
        """
        if not ULTRALYTICS_AVAILABLE:
            self.logger.error("ultralytics未安装，无法验证模型")
            return {}
        
        try:
            model = YOLO(weights_path)
            
            if dataset_yaml is None:
                dataset_yaml = self.config['dataset']['yaml_path']
            
            # 验证参数
            val_args = {
                'data': dataset_yaml,
                'imgsz': self.config['model']['input_size'],
                'conf': self.config['validation']['conf_threshold'],
                'iou': self.config['validation']['iou_threshold'],
                'max_det': self.config['validation']['max_det'],
                'plots': True,
                'save_txt': True,
                'save_conf': True
            }
            
            self.logger.info(f"开始验证模型: {weights_path}")
            
            # 执行验证
            results = model.val(**val_args)
            
            # 提取关键指标
            validation_result = {
                'weights_path': weights_path,
                'validation_time': datetime.now().isoformat(),
                'map50': float(results.box.map50),
                'map': float(results.box.map),
                'precision': float(results.box.mp),
                'recall': float(results.box.mr),
                'f1_score': 2 * (float(results.box.mp) * float(results.box.mr)) / 
                           (float(results.box.mp) + float(results.box.mr) + 1e-6),
                'validation_args': val_args
            }
            
            self.logger.info(f"验证完成！mAP@0.5: {validation_result['map50']:.4f}")
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"模型验证失败: {e}")
            return {}
    
    def transfer_weights(self, source_weights: str, target_config: Dict = None) -> str:
        """
        权重迁移和微调
        
        Args:
            source_weights: 源权重文件路径
            target_config: 目标配置（如果与当前配置不同）
            
        Returns:
            迁移后的权重文件路径
        """
        if not ULTRALYTICS_AVAILABLE:
            self.logger.error("ultralytics未安装，无法进行权重迁移")
            return ""
        
        try:
            # 加载源模型
            source_model = YOLO(source_weights)
            self.logger.info(f"加载源权重: {source_weights}")
            
            # 使用目标配置或当前配置
            config = target_config or self.config
            
            # 创建迁移学习的训练参数（较小的学习率和epochs）
            transfer_args = {
                'data': config['dataset']['yaml_path'],
                'epochs': config['training']['epochs'] // 2,  # 减少训练轮数
                'batch': config['training']['batch_size'],
                'imgsz': config['model']['input_size'],
                'lr0': config['training']['learning_rate'] * 0.1,  # 降低学习率
                'weight_decay': config['training']['weight_decay'],
                'momentum': config['training']['momentum'],
                'warmup_epochs': 1,
                'patience': config['training']['patience'] // 2,
                'device': config['training']['device'],
                'project': config['output']['project_dir'],
                'name': f"{config['output']['name']}_transfer",
                'plots': True,
                'exist_ok': True,
                'freeze': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]  # 冻结前面的层
            }
            
            self.logger.info("开始权重迁移训练...")
            
            # 迁移学习训练
            results = source_model.train(**transfer_args)
            
            transfer_weights_path = str(results.save_dir / 'weights' / 'best.pt')
            
            self.logger.info(f"权重迁移完成: {transfer_weights_path}")
            
            return transfer_weights_path
            
        except Exception as e:
            self.logger.error(f"权重迁移失败: {e}")
            return ""
    
    def hyperparameter_tuning(self, dataset_yaml: str, iterations: int = 10) -> Dict:
        """
        超参数调优
        
        Args:
            dataset_yaml: 数据集配置文件
            iterations: 调优迭代次数
            
        Returns:
            最佳超参数结果
        """
        if not ULTRALYTICS_AVAILABLE:
            self.logger.error("ultralytics未安装，无法进行超参数调优")
            return {}
        
        try:
            model = YOLO(self.config['model']['base_model'])
            
            # 超参数调优
            tuning_args = {
                'data': dataset_yaml,
                'epochs': 30,  # 较少的epochs用于快速调优
                'iterations': iterations,
                'optimizer': 'AdamW',
                'plots': False,  # 关闭绘图以节省时间
                'save': False,
                'project': self.config['output']['project_dir'],
                'name': f"{self.config['output']['name']}_tune"
            }
            
            self.logger.info(f"开始超参数调优，迭代次数: {iterations}")
            
            # 执行调优
            results = model.tune(**tuning_args)
            
            self.logger.info("超参数调优完成")
            
            return results
            
        except Exception as e:
            self.logger.error(f"超参数调优失败: {e}")
            return {}
    
    def export_model(self, weights_path: str, export_formats: List[str] = None) -> Dict[str, str]:
        """
        导出模型为不同格式
        
        Args:
            weights_path: 权重文件路径
            export_formats: 导出格式列表
            
        Returns:
            导出文件路径字典
        """
        if not ULTRALYTICS_AVAILABLE:
            self.logger.error("ultralytics未安装，无法导出模型")
            return {}
        
        if export_formats is None:
            export_formats = ['onnx', 'torchscript', 'tflite']
        
        try:
            model = YOLO(weights_path)
            export_paths = {}
            
            for fmt in export_formats:
                self.logger.info(f"导出{fmt}格式...")
                
                try:
                    if fmt == 'onnx':
                        path = model.export(format='onnx', simplify=True)
                    elif fmt == 'torchscript':
                        path = model.export(format='torchscript')
                    elif fmt == 'tflite':
                        path = model.export(format='tflite')
                    elif fmt == 'coreml':
                        path = model.export(format='coreml')
                    else:
                        path = model.export(format=fmt)
                    
                    export_paths[fmt] = str(path)
                    self.logger.info(f"{fmt}格式导出完成: {path}")
                    
                except Exception as e:
                    self.logger.error(f"导出{fmt}格式失败: {e}")
                    continue
            
            return export_paths
            
        except Exception as e:
            self.logger.error(f"模型导出失败: {e}")
            return {}
    
    def analyze_training_results(self, results_dir: str):
        """分析训练结果"""
        if not PLOT_AVAILABLE:
            self.logger.warning("绘图库未安装，跳过结果分析")
            return
        
        try:
            results_path = Path(results_dir)
            
            # 读取训练结果
            results_csv = results_path / "results.csv"
            if results_csv.exists():
                import pandas as pd
                df = pd.read_csv(results_csv)
                
                # 创建训练曲线图
                self._plot_training_curves(df, results_path)
                
                # 生成训练报告
                self._generate_training_report(df, results_path)
            
        except Exception as e:
            self.logger.error(f"分析训练结果失败: {e}")
    
    def _plot_training_curves(self, df, output_dir):
        """绘制训练曲线"""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('训练过程分析', fontsize=16)
            
            # 损失曲线
            axes[0, 0].plot(df['epoch'], df['train/box_loss'], label='训练Box损失')
            axes[0, 0].plot(df['epoch'], df['val/box_loss'], label='验证Box损失')
            axes[0, 0].set_title('Box损失')
            axes[0, 0].legend()
            axes[0, 0].grid(True)
            
            # mAP曲线
            axes[0, 1].plot(df['epoch'], df['metrics/mAP50(B)'], label='mAP@0.5')
            axes[0, 1].plot(df['epoch'], df['metrics/mAP50-95(B)'], label='mAP@0.5:0.95')
            axes[0, 1].set_title('mAP指标')
            axes[0, 1].legend()
            axes[0, 1].grid(True)
            
            # 精确率和召回率
            axes[1, 0].plot(df['epoch'], df['metrics/precision(B)'], label='精确率')
            axes[1, 0].plot(df['epoch'], df['metrics/recall(B)'], label='召回率')
            axes[1, 0].set_title('精确率和召回率')
            axes[1, 0].legend()
            axes[1, 0].grid(True)
            
            # 学习率
            axes[1, 1].plot(df['epoch'], df['lr/pg0'], label='学习率')
            axes[1, 1].set_title('学习率变化')
            axes[1, 1].legend()
            axes[1, 1].grid(True)
            
            plt.tight_layout()
            plt.savefig(output_dir / 'training_curves.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"训练曲线已保存: {output_dir / 'training_curves.png'}")
            
        except Exception as e:
            self.logger.error(f"绘制训练曲线失败: {e}")
    
    def _generate_training_report(self, df, output_dir):
        """生成训练报告"""
        try:
            # 找到最佳epoch
            best_epoch = df.loc[df['metrics/mAP50(B)'].idxmax()]
            
            report = {
                'training_summary': {
                    'total_epochs': len(df),
                    'best_epoch': int(best_epoch['epoch']),
                    'best_map50': float(best_epoch['metrics/mAP50(B)']),
                    'best_map50_95': float(best_epoch['metrics/mAP50-95(B)']),
                    'final_precision': float(df.iloc[-1]['metrics/precision(B)']),
                    'final_recall': float(df.iloc[-1]['metrics/recall(B)']),
                    'final_box_loss': float(df.iloc[-1]['train/box_loss']),
                    'final_val_loss': float(df.iloc[-1]['val/box_loss'])
                },
                'recommendations': self._generate_recommendations(df)
            }
            
            # 保存报告
            report_path = output_dir / 'training_report.json'
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"训练报告已保存: {report_path}")
            
        except Exception as e:
            self.logger.error(f"生成训练报告失败: {e}")
    
    def _generate_recommendations(self, df) -> List[str]:
        """生成训练建议"""
        recommendations = []
        
        try:
            # 检查过拟合
            final_train_loss = df.iloc[-1]['train/box_loss']
            final_val_loss = df.iloc[-1]['val/box_loss']
            
            if final_val_loss > final_train_loss * 1.5:
                recommendations.append("检测到过拟合现象，建议增加数据增强或减少模型复杂度")
            
            # 检查学习率
            lr_values = df['lr/pg0'].values
            if lr_values[-1] > lr_values[0] * 0.1:
                recommendations.append("学习率下降不够，建议延长训练或调整学习率调度")
            
            # 检查收敛
            last_10_map = df['metrics/mAP50(B)'].tail(10)
            if last_10_map.std() < 0.01:
                recommendations.append("模型已收敛，可以考虑早停或调整学习率")
            
            # 检查性能
            best_map50 = df['metrics/mAP50(B)'].max()
            if best_map50 < 0.5:
                recommendations.append("模型性能较低，建议检查数据质量或调整网络结构")
            elif best_map50 > 0.9:
                recommendations.append("模型性能优秀，可以考虑导出部署")
            
        except Exception as e:
            self.logger.error(f"生成建议失败: {e}")
        
        return recommendations


def create_training_config():
    """创建训练配置文件模板"""
    trainer = FinlessPorpoiseTrainer()
    config = trainer._get_default_training_config()
    
    with open('training_config.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    print("训练配置文件模板已创建: training_config.yaml")


def main():
    """训练模块命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description='江豚检测模型训练系统')
    parser.add_argument('--mode', choices=['train', 'validate', 'transfer', 'tune', 'export', 'config'], 
                       required=True, help='运行模式')
    parser.add_argument('--config', '-c', default='training_config.yaml', help='训练配置文件')
    parser.add_argument('--dataset', '-d', help='数据集目录')
    parser.add_argument('--weights', '-w', help='权重文件路径')
    parser.add_argument('--resume', help='恢复训练的检查点')
    parser.add_argument('--iterations', type=int, default=10, help='调优迭代次数')
    parser.add_argument('--formats', nargs='+', default=['onnx'], help='导出格式')
    
    args = parser.parse_args()
    
    if args.mode == 'config':
        create_training_config()
        return
    
    # 初始化训练器
    trainer = FinlessPorpoiseTrainer(args.config)
    
    if args.mode == 'train':
        if not args.dataset:
            print("错误: 训练模式需要指定数据集目录")
            return
        
        # 准备训练环境
        if trainer.prepare_training_environment(args.dataset):
            result = trainer.train_model(args.resume)
            if result:
                print(f"训练完成！最佳权重: {result['best_weights']}")
                
                # 分析训练结果
                trainer.analyze_training_results(result['results_dir'])
        
    elif args.mode == 'validate':
        if not args.weights:
            print("错误: 验证模式需要指定权重文件")
            return
        
        result = trainer.validate_model(args.weights)
        if result:
            print(f"验证完成！mAP@0.5: {result['map50']:.4f}")
    
    elif args.mode == 'transfer':
        if not args.weights or not args.dataset:
            print("错误: 迁移学习需要指定源权重文件和数据集目录")
            return
        
        trainer.prepare_training_environment(args.dataset)
        transfer_weights = trainer.transfer_weights(args.weights)
        if transfer_weights:
            print(f"权重迁移完成: {transfer_weights}")
    
    elif args.mode == 'tune':
        if not args.dataset:
            print("错误: 超参数调优需要指定数据集目录")
            return
        
        trainer.prepare_training_environment(args.dataset)
        result = trainer.hyperparameter_tuning(
            trainer.config['dataset']['yaml_path'], 
            args.iterations
        )
        if result:
            print("超参数调优完成")
    
    elif args.mode == 'export':
        if not args.weights:
            print("错误: 模型导出需要指定权重文件")
            return
        
        export_paths = trainer.export_model(args.weights, args.formats)
        if export_paths:
            print("模型导出完成:")
            for fmt, path in export_paths.items():
                print(f"  {fmt}: {path}")


if __name__ == "__main__":
    main() 