@echo off
chcp 65001 >nul
title Finless Porpoise Detection System - Conda Environment
color 0A

echo ================================================
echo   Finless Porpoise Detection System
echo   Conda Environment: yolov11
echo ================================================
echo.

:: Initialize conda (try different common paths)
echo Initializing conda...
if exist "%USERPROFILE%\anaconda3\Scripts\conda.exe" (
    call "%USERPROFILE%\anaconda3\Scripts\activate.bat" "%USERPROFILE%\anaconda3"
) else if exist "%USERPROFILE%\miniconda3\Scripts\conda.exe" (
    call "%USERPROFILE%\miniconda3\Scripts\activate.bat" "%USERPROFILE%\miniconda3"
) else if exist "C:\ProgramData\Anaconda3\Scripts\conda.exe" (
    call "C:\ProgramData\Anaconda3\Scripts\activate.bat" "C:\ProgramData\Anaconda3"
) else (
    echo [WARNING] Conda not found in common locations.
    echo Trying to use conda from PATH...
)

:: Activate yolov11 environment
echo.
echo Activating conda environment: yolov11
call conda activate yolov11
if %errorlevel% neq 0 (
    echo [ERROR] Failed to activate conda environment 'yolov11'.
    echo.
    echo Please check:
    echo 1. Conda is installed and available in PATH
    echo 2. Environment 'yolov11' exists
    echo 3. Run 'conda env list' to see available environments
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b
)

:: Verify environment
echo.
echo Verifying environment...
python --version
if %errorlevel% neq 0 (
    echo [ERROR] Python not available in yolov11 environment.
    echo Press any key to exit...
    pause >nul
    exit /b
)

:: Show environment info
echo.
echo Environment activated successfully!
echo Current Python: 
python -c "import sys; print(sys.executable)"
echo.

:: Check if run.py exists
if not exist run.py (
    echo [ERROR] run.py not found. Please run this script in the correct directory.
    echo Press any key to exit...
    pause >nul
    exit /b
)

:: Provide startup options
echo Please select startup method:
echo 1. Full startup process (Recommended)
echo 2. Direct web app startup
echo 3. System status check
echo 4. Fix dependencies
echo.
set /p choice="Please enter your choice (1/2/3/4): "

if "%choice%"=="1" (
    echo.
    echo Starting full process...
    python run.py
) else if "%choice%"=="2" (
    echo.
    echo Starting web app directly...
    python quick_start.py
) else if "%choice%"=="3" (
    echo.
    echo Running system status check...
    python system_status.py
) else if "%choice%"=="4" (
    echo.
    echo Fixing dependencies...
    python fix_dependencies.py
) else (
    echo.
    echo Invalid choice, starting default process...
    python run.py
)

echo.
echo Press any key to exit...
pause >nul 