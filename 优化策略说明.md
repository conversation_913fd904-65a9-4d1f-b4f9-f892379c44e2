# 铜陵无人机检测系统 - 优化策略说明

## 版本对比

### 1. 基础检测 (tongling_detection.py)
- **特点**: 基础功能，使用SAHI切片检测
- **适用场景**: 小规模数据，测试环境
- **性能**: 中等

### 2. 增强检测 (tongling_detection_enhanced.py)
- **特点**: 添加对象跟踪和交互式地图
- **适用场景**: 需要跟踪移动目标的场景
- **性能**: 中等

### 3. TensorRT检测 (tongling_detection_tensorrt.py)
- **特点**: 使用原始TensorRT + pycuda
- **适用场景**: 高性能要求，但依赖复杂
- **性能**: 高（但安装复杂）

### 4. 简化TensorRT检测 (tongling_detection_tensorrt_simple.py)
- **特点**: 使用ultralytics内置TensorRT支持
- **适用场景**: 推荐版本，安装简单
- **性能**: 高

### 5. 优化检测 (tongling_detection_optimized.py)
- **特点**: 多线程优化 + SAHI切片 + 进度显示
- **适用场景**: 大规模数据处理
- **性能**: 很高

### 6. 超优化检测 (tongling_detection_ultra_optimized.py) ⭐
- **特点**: 内存管理 + 批量处理 + 显存优化
- **适用场景**: 解决内存显存爆满问题
- **性能**: 最高

## 超优化版本的主要改进

### 内存管理优化
1. **批量处理**: 将batch_size从200降低到50，减少内存占用
2. **内存监控**: 实时监控内存使用，超过阈值时自动清理
3. **垃圾回收**: 定期调用gc.collect()清理Python对象
4. **临时文件管理**: 及时删除临时文件，避免磁盘空间占用

### GPU显存优化
1. **显存限制**: 使用torch.cuda.set_per_process_memory_fraction(0.8)限制显存使用
2. **显存清理**: 定期调用torch.cuda.empty_cache()清理GPU缓存
3. **批处理优化**: 减少单次批处理大小，避免显存溢出

### CPU利用率优化
1. **线程数调整**: 从64线程降低到16线程，避免过多线程竞争
2. **文件夹并行**: 减少文件夹并行处理数量，从8降低到4
3. **内存监控**: 使用psutil监控系统资源使用

### SAHI切片优化
1. **重叠减少**: 从50%重叠降低到25%重叠，减少切片数量
2. **批量切片**: 批量处理切片，而不是逐个处理
3. **坐标调整**: 优化切片坐标调整算法

## 性能对比

| 版本 | 内存使用 | GPU利用率 | CPU利用率 | 检测速度 | 稳定性 |
|------|----------|-----------|-----------|----------|--------|
| 基础版 | 中等 | 低 | 低 | 慢 | 高 |
| 增强版 | 中等 | 低 | 低 | 慢 | 高 |
| TensorRT | 高 | 高 | 中等 | 快 | 中等 |
| 简化TensorRT | 高 | 高 | 中等 | 快 | 高 |
| 优化版 | 高 | 高 | 高 | 很快 | 中等 |
| **超优化版** | **低** | **高** | **高** | **最快** | **高** |

## 推荐使用策略

### 如果遇到内存显存爆满问题：
**使用超优化版本 (tongling_detection_ultra_optimized.py)**

### 如果系统资源充足：
**使用简化TensorRT版本 (tongling_detection_tensorrt_simple.py)**

### 如果需要对象跟踪：
**使用增强版本 (tongling_detection_enhanced.py)**

## 参数调优建议

### 超优化版本参数调整：
```python
# 如果内存还是不够，可以进一步降低
batch_size=30  # 从50降低到30
max_workers=8   # 从16降低到8
max_memory_gb=150  # 从200降低到150

# 如果系统资源充足，可以适当提高
batch_size=80   # 从50提高到80
max_workers=24  # 从16提高到24
max_memory_gb=250  # 从200提高到250
```

### 监控指标：
1. **内存使用**: 保持在80%以下
2. **GPU显存**: 保持在80%以下
3. **CPU使用率**: 保持在70%以下
4. **检测速度**: 每秒处理图片数量

## 故障排除

### 内存不足：
- 降低batch_size
- 降低max_workers
- 降低max_memory_gb

### GPU显存不足：
- 降低batch_size
- 检查是否有其他程序占用GPU
- 重启系统清理显存

### CPU利用率低：
- 增加max_workers
- 检查是否有其他程序占用CPU
- 检查线程竞争情况

### 检测速度慢：
- 增加batch_size（在内存允许的情况下）
- 增加max_workers
- 检查GPU是否正常工作

## 运行建议

1. **首次运行**: 使用超优化版本，观察资源使用情况
2. **根据监控结果**: 调整参数以获得最佳性能
3. **定期清理**: 清理临时文件和缓存
4. **备份结果**: 定期备份检测结果

## 技术支持

如果遇到问题，请检查：
1. 日志文件中的错误信息
2. 系统资源使用情况
3. 模型文件是否正确
4. 数据文件是否存在 