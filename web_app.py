#!/usr/bin/env python3
"""
江豚检测系统 - Web版本
基于Flask的网页界面
"""

import os
import sys
import json
import uuid
from pathlib import Path
from datetime import datetime
import pandas as pd
import importlib.util

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for
from werkzeug.utils import secure_filename
import zipfile
import io
import base64
from PIL import Image
import cv2
import numpy as np

# 检查必要的依赖
def check_dependencies():
    """检查系统依赖"""
    deps = {
        'torch': False,
        'ultralytics': False,
        'sahi': False,
        'opencv': False,
        'gps_libs': False
    }
    
    try:
        import torch
        deps['torch'] = torch.__version__
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"🎯 CUDA可用: {torch.cuda.is_available()}")
    except ImportError:
        print("❌ PyTorch: 未安装")
    
    try:
        import ultralytics
        deps['ultralytics'] = ultralytics.__version__
        print(f"✅ Ultralytics: {ultralytics.__version__}")
    except ImportError:
        print("❌ Ultralytics: 未安装")
        
    try:
        import sahi
        deps['sahi'] = sahi.__version__
        print(f"✅ SAHI: {sahi.__version__}")
    except ImportError:
        print("❌ SAHI: 未安装")
        
    try:
        import cv2
        deps['opencv'] = cv2.__version__
        print(f"✅ OpenCV: {cv2.__version__}")
    except ImportError:
        print("❌ OpenCV: 未安装")
        
    try:
        import utm, geopy
        deps['gps_libs'] = "已安装"
        print("✅ GPS库: 已安装")
    except ImportError:
        print("❌ GPS库: 未安装")
    
    # 检查模型文件
    model_exists = os.path.exists('best.pt')
    if model_exists:
        print("✅ 模型文件: best.pt 存在")
    else:
        print("❌ 模型文件: best.pt 不存在")
    
    config_exists = os.path.exists('config.yaml')
    if config_exists:
        print("✅ 配置文件: config.yaml 存在")
    else:
        print("❌ 配置文件: config.yaml 不存在")
    
    return deps, model_exists, config_exists

# 导入检测器
try:
    from src.detector import FinlessPorpoiseDetector
    DETECTOR_AVAILABLE = True
    print("✅ 检测器模块: 导入成功")
except ImportError as e:
    DETECTOR_AVAILABLE = False
    print(f"❌ 检测器模块导入失败: {e}")

app = Flask(__name__)
app.config['SECRET_KEY'] = 'finless_porpoise_detection_2024'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['RESULTS_FOLDER'] = 'web_results'
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size

# 确保必要的目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['RESULTS_FOLDER'], exist_ok=True)

# 全局变量
detector = None

def init_detector():
    """初始化检测器"""
    global detector
    if DETECTOR_AVAILABLE:
        try:
            detector = FinlessPorpoiseDetector()
            if detector.yolo_model is not None:
                print("✅ 检测器初始化成功，YOLO模型已加载")
                return True
            else:
                print("⚠️ 检测器初始化成功，但YOLO模型未加载")
                return False
        except Exception as e:
            print(f"❌ 检测器初始化失败: {e}")
            import traceback
            print(traceback.format_exc())
            return False
    else:
        print("❌ 检测器模块不可用")
        return False

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/status')
def status():
    """系统状态API"""
    deps, model_exists, config_exists = check_dependencies()
    
    status_data = {
        'dependencies': deps,
        'model_file': model_exists,
        'config_file': config_exists,
        'detector_ready': detector is not None and detector.yolo_model is not None,
        'timestamp': datetime.now().isoformat()
    }
    
    return jsonify(status_data)

@app.route('/api/test')
def test_api():
    """测试API"""
    return jsonify({
        'status': 'ok',
        'message': '江豚检测系统API正常运行',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/upload', methods=['POST'])
def upload_file():
    """上传文件并检测"""
    if 'file' not in request.files:
        return jsonify({'error': '没有选择文件'}), 400
    
    file = request.files['file']
    
    if file.filename == '':
        return jsonify({'error': '没有选择文件'}), 400
    
    if file:
        # 保存上传的文件
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)
        
        # 获取置信度阈值
        confidence = float(request.form.get('confidence', 0.25))
        use_sahi = request.form.get('use_sahi', 'false').lower() == 'true'
        
        # 创建结果目录
        result_id = str(uuid.uuid4())
        result_dir = os.path.join(app.config['RESULTS_FOLDER'], result_id)
        os.makedirs(result_dir, exist_ok=True)
        
        # 执行检测
        detections = []
        result_image_path = ""
        
        if detector and detector.yolo_model is not None:
            try:
                # 更新检测器配置
                detector.config['model']['confidence_threshold'] = confidence
                if hasattr(detector, 'sahi_model') and detector.sahi_model:
                    detector.sahi_model.confidence_threshold = confidence
                
                # 执行检测
                detections = detector.detect_image(file_path, use_sahi=use_sahi)
                
                # 保存结果图片
                image = cv2.imread(file_path)
                result_image_name = f"result_{os.path.basename(file_path)}"
                result_image_path = os.path.join(result_dir, result_image_name)
                
                # 在图像上绘制检测框
                for detection in detections:
                    x1 = int(detection['bbox_x1'])
                    y1 = int(detection['bbox_y1'])
                    x2 = int(detection['bbox_x2'])
                    y2 = int(detection['bbox_y2'])
                    conf = detection['confidence']
                    
                    # 确保坐标在图像范围内
                    h, w = image.shape[:2]
                    x1 = max(0, min(x1, w-1))
                    y1 = max(0, min(y1, h-1))
                    x2 = max(0, min(x2, w-1))
                    y2 = max(0, min(y2, h-1))
                    
                    # 绘制边界框
                    cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    
                    # 绘制标签
                    label = f"江豚 {conf:.2f}"
                    text_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
                    cv2.rectangle(image, (x1, y1-text_size[1]-5), (x1+text_size[0], y1), (0, 255, 0), -1)
                    cv2.putText(image, label, (x1, y1-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
                
                cv2.imwrite(result_image_path, image)
                
            except Exception as e:
                print(f"检测过程中出错: {e}")
                import traceback
                print(traceback.format_exc())
        else:
            # 如果检测器不可用，创建一个空结果图片
            image = cv2.imread(file_path)
            result_image_name = f"result_{os.path.basename(file_path)}"
            result_image_path = os.path.join(result_dir, result_image_name)
            
            # 在图像上添加"检测器未初始化"文本
            h, w = image.shape[:2]
            cv2.putText(image, "检测器未初始化", (w//2-100, h//2), 
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            
            cv2.imwrite(result_image_path, image)
        
        # 准备响应数据
        result_url = f"/results/{result_id}/{result_image_name}"
        
        return jsonify({
            'success': True,
            'message': f'检测完成，发现 {len(detections)} 个目标',
            'detections': detections,
            'result_image': result_url,
            'confidence': confidence
        })

@app.route('/results/<result_id>/<filename>')
def results(result_id, filename):
    """提供结果图片"""
    return send_file(os.path.join(app.config['RESULTS_FOLDER'], result_id, filename))

if __name__ == '__main__':
    print("\n" + "="*50)
    print("🐋 江豚检测系统 - Web版本")
    print("="*50)
    
    # 检查依赖
    check_dependencies()
    
    print("\n正在初始化检测器...")
    detector_ready = init_detector()
    
    print("\n🚀 启动Web服务器...")
    print("📱 访问地址: http://localhost:5000")
    print("✅ 支持功能:")
    print("   - 批量图片检测")
    print("   - SAHI切片检测")
    print("   - GPS坐标计算")
    print("   - 检测结果可视化")
    print("   - CSV报告导出")
    print("-"*50)
    print("🌊 请在浏览器中打开上述地址开始使用")
    print("-"*50)
    
    app.run(host='0.0.0.0', port=5000, debug=True) 