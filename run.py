#!/usr/bin/env python3
"""
江豚检测系统 - 一键式启动脚本
自动修复依赖并启动系统
"""

import os
import sys
import time
import importlib.util
import subprocess
from pathlib import Path

def check_module(module_name):
    """检查模块是否已安装"""
    return importlib.util.find_spec(module_name) is not None

def run_script(script_name):
    """运行指定的Python脚本"""
    print(f"\n运行脚本: {script_name}...")
    result = subprocess.run(
        [sys.executable, script_name],
        capture_output=False,
        text=True
    )
    return result.returncode == 0

def check_conda_environment():
    """检查是否在正确的conda环境中"""
    try:
        import os
        conda_env = os.environ.get('CONDA_DEFAULT_ENV', '')
        if conda_env != 'yolov11':
            print(f"⚠️ 当前环境: {conda_env if conda_env else '未知'}")
            print("🔧 推荐使用 'yolov11' 环境")
            print("💡 请运行: conda activate yolov11")
            print("📁 或者双击 start_conda.bat 文件")
            print("-" * 50)
        else:
            print(f"✅ 当前环境: {conda_env}")
    except Exception as e:
        print(f"⚠️ 环境检查失败: {e}")

def main():
    """主函数"""
    print("\n" + "="*50)
    print("🐋 江豚检测系统 - 一键式启动")
    print("="*50)
    
    # 检查conda环境
    check_conda_environment()
    
    # 1. 检查系统状态
    print("\n第1步: 检查系统状态")
    if Path("system_status.py").exists():
        run_script("system_status.py")
    else:
        print("❌ 系统状态检查脚本不存在")
    
    # 2. 修复依赖
    print("\n第2步: 修复依赖")
    if Path("fix_dependencies.py").exists():
        run_script("fix_dependencies.py")
    else:
        print("❌ 依赖修复脚本不存在")
    
    # 3. 测试检测器
    print("\n第3步: 测试检测器")
    print("选择测试模式:")
    print("  a. 快速测试 (推荐)")
    print("  b. 完整测试")
    
    test_choice = input("请选择测试模式 (a/b): ").strip().lower()
    
    if test_choice == "a":
        if Path("quick_test.py").exists():
            print("运行快速测试...")
            run_script("quick_test.py")
        else:
            print("❌ 快速测试脚本不存在")
    elif test_choice == "b":
        if Path("test_detector.py").exists():
            print("运行完整测试...")
            run_script("test_detector.py")
        else:
            print("❌ 检测器测试脚本不存在")
    else:
        print("跳过测试步骤")
    
    # 4. 启动Web应用
    print("\n第4步: 启动Web应用")
    print("请选择要启动的Web应用:")
    print("1. 标准Web应用 (web_app.py)")
    print("2. 增强版Web应用 (enhanced_web_app.py)")
    print("3. 跳过启动")
    
    while True:
        choice = input("请输入选择 (1/2/3): ").strip()
        
        if choice == "1":
            if Path("web_app.py").exists():
                print("\n正在启动标准Web应用...")
                print("Web应用将在 http://localhost:5000 上运行")
                print("按 Ctrl+C 停止应用")
                print("\n" + "="*50)
                time.sleep(2)
                run_script("web_app.py")
                break
            else:
                print("❌ 标准Web应用脚本不存在")
                break
        elif choice == "2":
            if Path("enhanced_web_app.py").exists():
                print("\n正在启动增强版Web应用...")
                print("Web应用将在 http://localhost:5000 上运行")
                print("按 Ctrl+C 停止应用")
                print("\n" + "="*50)
                time.sleep(2)
                run_script("enhanced_web_app.py")
                break
            else:
                print("❌ 增强版Web应用脚本不存在")
                break
        elif choice == "3":
            print("跳过启动Web应用")
            break
        else:
            print("❌ 无效的选择，请输入 1、2 或 3")
    
    print("\n" + "="*50)
    print("感谢使用江豚检测系统!")
    print("="*50)

if __name__ == "__main__":
    main() 