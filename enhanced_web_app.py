#!/usr/bin/env python3
"""
江豚检测系统 - 增强版Web应用
支持WebSocket实时进度更新和现代化API接口
"""

import os
import sys
import json
import uuid
import asyncio
from pathlib import Path
from datetime import datetime
import pandas as pd
import threading
import time

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from flask import Flask, render_template, request, jsonify, send_file
from flask_socketio import SocketIO, emit
from werkzeug.utils import secure_filename
import zipfile
import io
import base64
from PIL import Image
import cv2
import numpy as np

# 导入检测器
try:
    from src.detector import FinlessPorpoiseDetector
    DETECTOR_AVAILABLE = True
except ImportError as e:
    print(f"警告: 检测器模块加载失败: {e}")
    DETECTOR_AVAILABLE = False

app = Flask(__name__)
app.config['SECRET_KEY'] = 'finless_porpoise_detection_2024'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['RESULTS_FOLDER'] = 'web_results'
app.config['MAX_CONTENT_LENGTH'] = 200 * 1024 * 1024  # 200MB max file size

# 初始化SocketIO
socketio = SocketIO(app, cors_allowed_origins="*", logger=True, engineio_logger=True)

# 确保必要的目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['RESULTS_FOLDER'], exist_ok=True)
os.makedirs('static/images', exist_ok=True)

# 全局变量
detector = None
current_tasks = {}

def init_detector():
    """初始化检测器"""
    global detector
    if DETECTOR_AVAILABLE:
        try:
            detector = FinlessPorpoiseDetector()
            print("✅ 检测器初始化成功")
            return True
        except Exception as e:
            print(f"❌ 检测器初始化失败: {e}")
            return False
    else:
        print("❌ 检测器模块不可用")
        return False

def emit_progress(task_id, step, total_steps, message, current_file="", extra_data=None):
    """发送进度更新到客户端"""
    progress_data = {
        'task_id': task_id,
        'currentStep': step,
        'totalSteps': total_steps,
        'message': message,
        'currentFile': current_file,
        'percentage': round((step / total_steps * 100), 1) if total_steps > 0 else 0,
        'timestamp': datetime.now().isoformat()
    }
    
    if extra_data:
        progress_data.update(extra_data)
    
    socketio.emit('progress_update', progress_data)
    
    # 更新任务状态
    if task_id in current_tasks:
        current_tasks[task_id].update(progress_data)

def check_dependencies():
    """检查系统依赖"""
    deps = {
        'torch': False,
        'ultralytics': False,
        'sahi': False,
        'opencv': False,
        'gps_libs': False
    }
    
    try:
        import torch
        deps['torch'] = torch.__version__
    except ImportError:
        pass
    
    try:
        import ultralytics
        deps['ultralytics'] = ultralytics.__version__
    except ImportError:
        pass
        
    try:
        import sahi
        deps['sahi'] = sahi.__version__
    except ImportError:
        pass
        
    try:
        import cv2
        deps['opencv'] = cv2.__version__
    except ImportError:
        pass
        
    try:
        import utm, geopy
        deps['gps_libs'] = "已安装"
    except ImportError:
        pass
    
    # 检查模型文件
    model_exists = os.path.exists('best.pt')
    config_exists = os.path.exists('config.yaml')
    
    return {
        'dependencies': deps,
        'model_file': model_exists,
        'config_file': config_exists,
        'detector_available': DETECTOR_AVAILABLE
    }

@app.route('/')
def index():
    """主页"""
    return render_template('enhanced_index.html')

@app.route('/api/system-status')
def system_status():
    """系统状态检查API"""
    status = check_dependencies()
    return jsonify(status)

@app.route('/api/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'detector_ready': detector is not None
    })

@socketio.on('connect')
def handle_connect():
    """WebSocket连接处理"""
    print(f"客户端已连接: {request.sid}")
    emit('connected', {'message': '已连接到江豚检测系统'})

@socketio.on('disconnect')
def handle_disconnect():
    """WebSocket断开连接处理"""
    print(f"客户端已断开: {request.sid}")

@socketio.on('start_detection')
def handle_detection(data):
    """处理检测任务"""
    task_id = str(uuid.uuid4())
    
    # 创建任务记录
    current_tasks[task_id] = {
        'status': 'starting',
        'created_at': datetime.now().isoformat(),
        'client_id': request.sid
    }
    
    # 启动后台检测任务
    thread = threading.Thread(
        target=run_detection_task,
        args=(task_id, data)
    )
    thread.daemon = True
    thread.start()
    
    emit('task_started', {'task_id': task_id})

def run_detection_task(task_id, data):
    """在后台运行检测任务"""
    try:
        files = data.get('files', [])
        settings = data.get('settings', {})
        
        total_files = len(files)
        total_steps = total_files * 4  # 每个文件4个步骤
        
        emit_progress(task_id, 0, total_steps, "正在初始化检测任务...")
        
        if not detector:
            emit_progress(task_id, 0, total_steps, "检测器未初始化，请检查系统配置", extra_data={'error': True})
            return
        
        # 更新检测器设置
        confidence = settings.get('confidence', 0.25)
        use_sahi = settings.get('useSahi', False)
        
        if hasattr(detector, 'config'):
            detector.config['model']['confidence_threshold'] = confidence
            
        results = []
        current_step = 0
        
        for i, file_info in enumerate(files):
            filename = file_info['name']
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            
            if not os.path.exists(file_path):
                emit_progress(task_id, current_step, total_steps, f"文件未找到: {filename}", filename, {'error': True})
                continue
            
            # 步骤1: 读取图片
            current_step += 1
            emit_progress(task_id, current_step, total_steps, f"正在读取图片...", filename)
            time.sleep(0.5)  # 模拟处理时间
            
            # 步骤2: 提取GPS信息
            current_step += 1
            emit_progress(task_id, current_step, total_steps, f"正在提取GPS信息...", filename)
            time.sleep(0.3)
            
            # 步骤3: 执行检测
            current_step += 1
            emit_progress(task_id, current_step, total_steps, f"正在执行AI检测...", filename, {'detecting': True})
            
            try:
                # 实际检测逻辑
                detections = detector.detect_image(file_path, confidence_threshold=confidence, use_sahi=use_sahi)
                
                # 步骤4: 生成结果
                current_step += 1
                emit_progress(task_id, current_step, total_steps, f"正在生成检测结果...", filename)
                
                # 处理检测结果
                for detection in detections:
                    result = {
                        'filename': filename,
                        'detection_method': detection.get('detection_method', 'YOLO'),
                        'confidence': detection.get('confidence', 0),
                        'bbox_x1': detection.get('bbox_x1', 0),
                        'bbox_y1': detection.get('bbox_y1', 0),
                        'bbox_x2': detection.get('bbox_x2', 0),
                        'bbox_y2': detection.get('bbox_y2', 0),
                        'latitude': detection.get('latitude', ''),
                        'longitude': detection.get('longitude', ''),
                        'timestamp': detection.get('timestamp', ''),
                    }
                    results.append(result)
                
                time.sleep(0.2)
                
            except Exception as e:
                emit_progress(task_id, current_step, total_steps, f"检测失败: {str(e)}", filename, {'error': True})
                current_step += 1  # 跳过生成结果步骤
                continue
        
        # 完成检测
        total_detections = len(results)
        emit_progress(task_id, total_steps, total_steps, f"检测完成！共发现 {total_detections} 个目标", extra_data={
            'completed': True,
            'total_detections': total_detections,
            'results': results
        })
        
        # 更新任务状态
        current_tasks[task_id]['status'] = 'completed'
        current_tasks[task_id]['results'] = results
        
    except Exception as e:
        emit_progress(task_id, 0, 1, f"任务执行失败: {str(e)}", extra_data={'error': True})
        current_tasks[task_id]['status'] = 'failed'

@app.route('/api/upload', methods=['POST'])
def upload_files():
    """文件上传API"""
    try:
        uploaded_files = request.files.getlist('files')
        
        if not uploaded_files:
            return jsonify({'error': '没有选择文件'}), 400
        
        file_info = []
        for file in uploaded_files:
            if file and file.filename:
                filename = secure_filename(file.filename)
                # 添加时间戳避免文件名冲突
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                base_name, ext = os.path.splitext(filename)
                unique_filename = f"{base_name}_{timestamp}{ext}"
                
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
                file.save(file_path)
                
                # 获取文件信息
                file_size = os.path.getsize(file_path)
                file_info.append({
                    'name': unique_filename,
                    'original_name': filename,
                    'size': file_size,
                    'uploaded_at': datetime.now().isoformat()
                })
        
        return jsonify({
            'message': f'成功上传 {len(file_info)} 个文件',
            'files': file_info
        })
        
    except Exception as e:
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

@app.route('/api/tasks/<task_id>')
def get_task_status(task_id):
    """获取任务状态"""
    if task_id in current_tasks:
        return jsonify(current_tasks[task_id])
    else:
        return jsonify({'error': '任务不存在'}), 404

@app.route('/api/download-results/<task_id>')
def download_results(task_id):
    """下载检测结果"""
    if task_id not in current_tasks:
        return jsonify({'error': '任务不存在'}), 404
    
    task = current_tasks[task_id]
    if task.get('status') != 'completed':
        return jsonify({'error': '任务未完成'}), 400
    
    results = task.get('results', [])
    if not results:
        return jsonify({'error': '没有检测结果'}), 404
    
    # 创建CSV文件
    df = pd.DataFrame(results)
    csv_buffer = io.StringIO()
    df.to_csv(csv_buffer, index=False, encoding='utf-8-sig')
    
    # 创建内存文件
    mem_file = io.BytesIO()
    mem_file.write(csv_buffer.getvalue().encode('utf-8-sig'))
    mem_file.seek(0)
    
    filename = f"江豚检测结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    return send_file(
        mem_file,
        mimetype='text/csv',
        as_attachment=True,
        download_name=filename
    )

if __name__ == '__main__':
    print("\n" + "="*50)
    print("🐋 江豚检测系统 - 增强版Web应用")
    print("="*50)
    
    print("正在初始化检测器...")
    detector_ready = init_detector()
    
    if detector_ready:
        print("✅ 检测器初始化成功")
    else:
        print("⚠️  检测器初始化失败，系统将以有限功能运行")
    
    print("\n🚀 启动增强版Web服务器...")
    print("🌟 新特性:")
    print("   - WebSocket实时进度更新")
    print("   - 现代化API接口")
    print("   - 任务状态管理")
    print("   - 文件批量上传")
    
    print(f"\n📱 访问地址:")
    print(f"   - 本地: http://localhost:5000")
    print(f"   - 局域网: http://*************:5000")
    
    print("\n🔗 支持的端点:")
    print("   - GET  /api/system-status  - 系统状态检查")
    print("   - GET  /api/health         - 健康检查")
    print("   - POST /api/upload         - 文件上传")
    print("   - WS   /socket.io          - WebSocket连接")
    
    print("-"*50)
    print("🌊 请在浏览器中打开上述地址开始使用")
    print("-"*50)
    
    # 启动服务器
    socketio.run(
        app,
        host='0.0.0.0',
        port=5000,
        debug=True,
        allow_unsafe_werkzeug=True
    ) 