#!/usr/bin/env python3
"""
江豚检测系统 - 检测器测试脚本
测试检测器是否正常工作
"""

import os
import sys
from pathlib import Path
import importlib.util
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_detector')

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def check_module(module_name):
    """检查模块是否已安装"""
    return importlib.util.find_spec(module_name) is not None

def main():
    """主函数"""
    print("\n" + "="*50)
    print("🐋 江豚检测系统 - 检测器测试")
    print("="*50)
    
    # 检查必要的模块
    required_modules = ["ultralytics", "sahi"]
    missing_modules = []
    
    for module in required_modules:
        if not check_module(module):
            missing_modules.append(module)
    
    if missing_modules:
        print(f"⚠️ 缺少必要的模块: {', '.join(missing_modules)}")
        print("请先运行修复脚本: python fix_dependencies.py")
        return
    
    # 检查模型文件
    model_path = Path("best.pt")
    if not model_path.exists():
        print("⚠️ 模型文件不存在: best.pt")
        print("请确保模型文件放置在正确位置")
        return
    
    # 导入检测器
    try:
        from src.detector import FinlessPorpoiseDetector
        print("✅ 成功导入检测器模块")
    except ImportError as e:
        print(f"❌ 导入检测器模块失败: {e}")
        import traceback
        print(traceback.format_exc())
        return
    
    # 初始化检测器
    try:
        detector = FinlessPorpoiseDetector()
        if detector.yolo_model is not None:
            print("✅ 检测器初始化成功，YOLO模型已加载")
        else:
            print("⚠️ 检测器初始化成功，但YOLO模型未加载")
            return
    except Exception as e:
        print(f"❌ 检测器初始化失败: {e}")
        import traceback
        print(traceback.format_exc())
        return
    
    # 检查uploads目录
    uploads_dir = Path("uploads")
    if not uploads_dir.exists():
        uploads_dir.mkdir(exist_ok=True)
        print(f"✅ 创建目录: {uploads_dir}")
    
    # 检查是否有测试图像
    test_images = list(uploads_dir.glob("*.jpg")) + list(uploads_dir.glob("*.png"))
    
    if not test_images:
        print("⚠️ uploads目录中没有测试图像，请添加测试图像")
        return
    
    # 创建结果目录
    results_dir = Path("test_results")
    results_dir.mkdir(exist_ok=True)
    print(f"✅ 创建结果目录: {results_dir}")
    
    # 测试检测
    print("\n开始测试检测...")
    for image_path in test_images:
        print(f"\n处理图像: {image_path}")
        try:
            # 执行检测
            detections = detector.detect_image(str(image_path))
            
            # 打印检测结果
            print(f"检测到 {len(detections)} 个目标:")
            for i, detection in enumerate(detections):
                confidence = detection.get('confidence', 0)
                x1 = detection.get('bbox_x1', 0)
                y1 = detection.get('bbox_y1', 0)
                x2 = detection.get('bbox_x2', 0)
                y2 = detection.get('bbox_y2', 0)
                method = detection.get('detection_method', 'Unknown')
                bbox = [x1, y1, x2, y2]
                print(f"  目标 {i+1}: 置信度={confidence:.2f}, 边界框={bbox}, 方法={method}")
                
                # 如果有GPS信息，也显示出来
                if 'latitude' in detection and 'longitude' in detection:
                    lat = detection['latitude']
                    lon = detection['longitude']
                    print(f"    GPS坐标: ({lat:.6f}, {lon:.6f})")
            
            # 保存标注图像
            output_path = results_dir / f"result_{image_path.name}"
            detector._save_annotated_image(str(image_path), detections, str(results_dir))
            print(f"✅ 结果已保存至: {output_path}")
            
        except Exception as e:
            print(f"❌ 检测失败: {e}")
            import traceback
            print(traceback.format_exc())
    
    print("\n" + "="*50)
    print("🎉 测试完成")
    print("="*50)

if __name__ == "__main__":
    main() 