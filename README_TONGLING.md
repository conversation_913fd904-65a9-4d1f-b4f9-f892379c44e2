# 铜陵无人机数据检测系统

## 系统概述

这是一个专门用于处理铜陵无人机数据的江豚检测系统。系统能够处理无人机拍摄的视频和图片，提取GPS信息，进行目标检测和追踪，并生成详细的分析报告。

## 主要功能

### 🎯 检测功能
- **多格式支持**: 支持图片(.jpg, .jpeg, .png, .bmp, .tiff)和视频(.mp4, .avi, .mov, .mkv)
- **SAHI切片检测**: 使用1280x1280切片进行高精度检测
- **目标追踪**: 支持视频中的目标追踪，识别运动目标
- **GPS信息提取**: 从图片EXIF和视频SRT字幕中提取GPS坐标

### 📊 分析功能
- **置信度分析**: 统计检测置信度分布
- **GPS分布图**: 生成检测目标的地理分布图
- **追踪轨迹**: 绘制目标运动轨迹
- **详细报告**: 生成Markdown格式的检测报告

### 🗺️ 可视化功能
- **交互式地图**: 使用Folium生成HTML地图
- **统计图表**: 生成置信度分布等统计图表
- **轨迹可视化**: 在地图上显示目标运动轨迹

## 系统要求

### 硬件要求
- **CPU**: 64核心处理器（推荐）
- **GPU**: NVIDIA RTX 4090或更高（推荐）
- **内存**: 32GB或更高
- **存储**: 足够的空间存储检测结果

### 软件要求
- **Python**: 3.8或更高版本
- **CUDA**: 11.8或更高版本（用于GPU加速）

## 安装步骤

### 1. 克隆或下载项目
确保项目文件夹包含以下文件：
- `best.engine` - YOLO模型文件
- `20250718铜陵/` - 数据文件夹
- 各种Python脚本文件

### 2. 安装依赖
运行依赖安装脚本：
```bash
python install_dependencies.py
```

或者手动安装：
```bash
pip install ultralytics>=8.0.0 sahi>=0.11.0 opencv-python>=4.8.0 pandas>=1.5.0 numpy>=1.21.0 matplotlib>=3.5.0 seaborn>=0.11.0 folium>=0.14.0 exifread>=3.0.0 torch>=1.13.0 torchvision>=0.14.0
```

### 3. 检查系统
运行启动脚本检查系统状态：
```bash
python run_tongling_detection.py
```

## 使用方法

### 快速开始

1. **运行启动脚本**：
   ```bash
   python run_tongling_detection.py
   ```

2. **选择检测模式**：
   - 基础检测：标准检测功能
   - 增强检测：包含目标追踪和地图可视化（推荐）

3. **等待检测完成**：
   系统会自动处理所有数据文件夹

### 手动运行

#### 基础检测
```bash
python tongling_detection.py
```

#### 增强检测（推荐）
```bash
python tongling_detection_enhanced.py
```

## 数据格式

### 文件夹结构
```
20250718铜陵/
├── 01铜陵80m速3.7ms角45°带偏录制/
│   ├── video1.mp4
│   ├── video1.srt
│   └── image1.jpg
├── 02铜陵80m速6ms角45°带偏录制/
│   └── ...
└── ...
```

### 支持的文件格式
- **图片**: .jpg, .jpeg, .png, .bmp, .tiff
- **视频**: .mp4, .avi, .mov, .mkv
- **字幕**: .srt, .SRT（包含GPS信息）

### GPS信息格式
- **图片EXIF**: 自动提取GPS坐标、高度、时间戳
- **SRT字幕**: 解析时间戳和GPS坐标信息

## 检测参数

### 主要参数
- **置信度阈值**: 0.5（可调整）
- **批处理大小**: 200（优化GPU使用）
- **SAHI切片大小**: 1280x1280
- **视频帧提取**: 每3秒提取一帧

### 性能优化
- **多线程处理**: 4个并行线程
- **GPU加速**: 自动检测并使用CUDA
- **内存管理**: 分批处理大文件

## 输出结果

### 结果文件夹结构
```
tongling_results_enhanced/
├── 01铜陵80m速3.7ms角45°带偏录制/
│   ├── 01铜陵80m速3.7ms角45°带偏录制_detections.csv
│   ├── 01铜陵80m速3.7ms角45°带偏录制_detections.json
│   ├── 01铜陵80m速3.7ms角45°带偏录制_report.md
│   ├── 01铜陵80m速3.7ms角45°带偏录制_confidence_distribution.png
│   ├── 01铜陵80m速3.7ms角45°带偏录制_gps_distribution.png
│   └── 01铜陵80m速3.7ms角45°带偏录制_map.html
├── 总体检测报告.md
└── 总体检测地图.html
```

### 输出文件说明

#### CSV文件
包含所有检测结果的详细数据：
- 边界框坐标
- 置信度
- GPS坐标
- 追踪ID
- 检测方法

#### JSON文件
结构化的检测结果数据，便于程序处理

#### Markdown报告
包含统计信息和检测摘要：
- 检测数量统计
- 置信度分析
- 检测方法分布

#### 图表文件
- **置信度分布图**: 显示检测置信度的分布情况
- **GPS分布图**: 显示检测目标的地理分布

#### HTML地图
- **交互式地图**: 可点击查看详细信息
- **颜色编码**: 根据置信度显示不同颜色
- **轨迹显示**: 显示目标运动轨迹

## 配置选项

### 模型配置
```python
# 在脚本中修改这些参数
confidence_threshold = 0.5  # 置信度阈值
batch_size = 200           # 批处理大小
slice_size = 1280          # SAHI切片大小
```

### 检测参数
```python
# 视频处理参数
frame_interval = 3         # 每3秒提取一帧
tracking_enabled = True    # 启用目标追踪
```

## 故障排除

### 常见问题

#### 1. 模型加载失败
**问题**: `best.engine`文件不存在或损坏
**解决**: 确保模型文件在正确位置，检查文件完整性

#### 2. CUDA内存不足
**问题**: GPU内存不够处理大批次
**解决**: 减小batch_size参数，或使用CPU模式

#### 3. 依赖包缺失
**问题**: 导入模块失败
**解决**: 运行`python install_dependencies.py`

#### 4. 文件编码问题
**问题**: 中文路径或文件名显示乱码
**解决**: 确保系统支持UTF-8编码

### 性能优化建议

1. **GPU优化**:
   - 确保CUDA版本与PyTorch兼容
   - 调整batch_size以充分利用GPU内存
   - 使用混合精度训练

2. **内存优化**:
   - 分批处理大文件
   - 及时清理临时文件
   - 使用多线程而非多进程

3. **存储优化**:
   - 使用SSD存储提高I/O性能
   - 定期清理临时文件
   - 压缩存储结果文件

## 技术细节

### 检测算法
- **YOLOv8**: 主要检测模型
- **SAHI**: 切片辅助超推理
- **ByteTracker**: 目标追踪算法

### GPS处理
- **EXIF解析**: 提取图片GPS信息
- **SRT解析**: 解析视频字幕中的GPS数据
- **坐标转换**: 处理不同坐标系统

### 可视化技术
- **Folium**: 交互式地图生成
- **Matplotlib**: 统计图表绘制
- **Seaborn**: 数据可视化增强

## 更新日志

### v1.0.0 (2024-07-18)
- 初始版本发布
- 支持基础检测功能
- 添加GPS信息提取
- 实现SAHI切片检测

### v1.1.0 (2024-07-18)
- 添加目标追踪功能
- 增强地图可视化
- 优化性能配置
- 完善错误处理

## 许可证

本项目仅供学术研究使用。

## 联系方式

如有问题或建议，请联系开发团队。

---

**注意**: 请确保在使用前备份重要数据，系统会处理大量文件并生成大量输出文件。 