#!/usr/bin/env python3
"""
铜陵无人机数据检测脚本 - 模块化终极优化版本
将任务分解为数据准备、模型检测和报告生成三个阶段
"""

import os
import sys
import cv2
import json
import csv
import logging
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Tuple, Optional
import exifread
import re
import torch
from ultralytics import YOLO
from ultralytics.utils.autobatch import autobatch
from collections import defaultdict
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings
import matplotlib.pyplot as plt
import seaborn as sns
import folium
import tempfile
import uuid
from tqdm import tqdm
import multiprocessing as mp
from functools import partial
import gc
import psutil
import subprocess # 新增导入 subprocess
import shutil # 新增导入 shutil 模块
import ffmpeg # 新增导入 ffmpeg-python
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class MemoryMonitor:
    """内存监控器"""
    def __init__(self, max_memory_gb: float):
        self.max_memory_gb = max_memory_gb
        self.max_memory_bytes = max_memory_gb * 1024**3
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler()]
        )
        self.logger = logging.getLogger('MemoryMonitor')

    def check_memory(self) -> bool:
        """检查内存使用"""
        memory = psutil.virtual_memory()
        return memory.used < self.max_memory_bytes

    def log_memory_usage(self):
        """记录内存使用"""
        memory = psutil.virtual_memory()
        used_gb = memory.used / 1024**3
        total_gb = memory.total / 1024**3
        self.logger.info(f"内存使用: {used_gb:.1f}GB / {total_gb:.1f}GB ({memory.percent}%)")

class DataLoader:
    """
    数据加载和预处理模块
    负责文件扫描、GPS提取、视频抽帧和SAHI切片
    """
    def __init__(self, base_path: str, slice_size: int = 1280, max_workers: int = None, temp_dir: Path = None, logger: logging.Logger = None, ffmpeg_executable_path: Optional[str] = None):
        self.base_path = Path(base_path)
        self.slice_size = slice_size
        self.max_workers = max_workers or min(64, mp.cpu_count() * 2) # 利用所有CPU核心
        self.temp_dir = temp_dir if temp_dir else Path(tempfile.mkdtemp(prefix="tongling_data_loader_"))
        self.temp_dir.mkdir(exist_ok=True)
        self.logger = logger or logging.getLogger(__name__)
        self.ffmpeg_executable_path = ffmpeg_executable_path

    def _convert_to_degrees(self, values) -> float:
        """将GPS坐标转换为度"""
        d = float(values[0].num) / float(values[0].den)
        m = float(values[1].num) / float(values[1].den)
        s = float(values[2].num) / float(values[2].den)
        return d + (m / 60.0) + (s / 3600.0)

    def extract_gps_from_exif(self, image_path: str) -> Dict[str, float]:
        """从图片EXIF信息中提取GPS坐标"""
        try:
            with open(image_path, 'rb') as f:
                tags = exifread.process_file(f)
            gps_info = {}
            if 'GPS GPSLatitude' in tags and 'GPS GPSLongitude' in tags:
                lat = self._convert_to_degrees(tags['GPS GPSLatitude'].values)
                lon = self._convert_to_degrees(tags['GPS GPSLongitude'].values)
                if 'GPS GPSLatitudeRef' in tags and tags['GPS GPSLatitudeRef'].values == 'S':
                    lat = -lat
                if 'GPS GPSLongitudeRef' in tags and tags['GPS GPSLongitudeRef'].values == 'W':
                    lon = -lon
                gps_info['latitude'] = lat
                gps_info['longitude'] = lon
            if 'GPS GPSAltitude' in tags:
                altitude = float(tags['GPS GPSAltitude'].values[0])
                gps_info['altitude'] = altitude
            if 'EXIF DateTimeOriginal' in tags:
                gps_info['timestamp'] = str(tags['EXIF DateTimeOriginal'].values)
            return gps_info
        except Exception as e:
            self.logger.warning(f"提取GPS信息失败 {image_path}: {e}")
            return {}

    def _parse_srt_time(self, time_str: str) -> float:
        """解析SRT时间格式"""
        try:
            time_parts = time_str.replace(',', '.').split(':')
            hours = int(time_parts[0])
            minutes = int(time_parts[1])
            seconds = float(time_parts[2])
            return hours * 3600 + minutes * 60 + seconds
        except:
            return 0.0

    def extract_gps_from_srt(self, srt_path: str) -> List[Dict]:
        """从SRT字幕文件中提取GPS信息"""
        gps_data = []
        try:
            with open(srt_path, 'r', encoding='utf-8') as f:
                content = f.read()
            blocks = content.strip().split('\n\n')
            for block in blocks:
                lines = block.strip().split('\n')
                if len(lines) >= 3:
                    time_line = lines[1]
                    time_match = re.search(r'(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})', time_line)
                    if time_match:
                        start_time = time_match.group(1)
                        end_time = time_match.group(2)
                        gps_info = {}
                        for line in lines[2:]:
                            if 'GPS' in line or 'latitude' in line.lower() or 'longitude' in line.lower():
                                lat_match = re.search(r'lat[itude]*[:\s]*([-\d.]+)', line, re.IGNORECASE)
                                lon_match = re.search(r'lon[gitude]*[:\s]*([-\d.]+)', line, re.IGNORECASE)
                                alt_match = re.search(r'alt[itude]*[:\s]*([-\d.]+)', line, re.IGNORECASE)
                                if lat_match:
                                    gps_info['latitude'] = float(lat_match.group(1))
                                if lon_match:
                                    gps_info['longitude'] = float(lon_match.group(1))
                                if alt_match:
                                    gps_info['altitude'] = float(alt_match.group(1))
                        if gps_info:
                            gps_info['start_time'] = start_time
                            gps_info['end_time'] = end_time
                            gps_data.append(gps_info)
        except Exception as e:
            self.logger.warning(f"提取SRT GPS信息失败 {srt_path}: {e}")
        return gps_data

    def _get_gps_at_time(self, gps_data: List[Dict], current_time: float) -> Dict:
        """根据时间获取对应的GPS信息"""
        if not gps_data:
            return {}
        for gps_record in gps_data:
            start_time = self._parse_srt_time(gps_record.get('start_time', '00:00:00,000'))
            end_time = self._parse_srt_time(gps_record.get('end_time', '00:00:00,000'))
            if start_time <= current_time <= end_time:
                return gps_record
        return {}

    def _process_video_for_frames(self, video_path: Path, srt_path: Optional[Path]) -> List[Dict]:
        """处理视频以提取帧并进行SAHI切片 (使用FFmpeg加速抽帧)"""
        self.logger.info(f"正在使用FFmpeg从视频中抽取帧: {video_path.name}")

        # 检查FFmpeg是否可用 (ffmpeg-python会自动查找，此处可移除)
        # if shutil.which("ffmpeg") is None:
        #     self.logger.error("FFmpeg 未安装或不在系统 PATH 中。请安装 FFmpeg。")
        #     raise RuntimeError("FFmpeg not found. Please install it.")

        temp_output_dir = self.temp_dir / f"frames_{video_path.stem}_{uuid.uuid4().hex}"
        temp_output_dir.mkdir(parents=True, exist_ok=True)
        
        # FFmpeg 命令，每3秒抽取一帧
        output_pattern = str(temp_output_dir / "frame_%08d.jpg")
        
        try:
            (ffmpeg
                .input(str(video_path))
                .output(output_pattern, vf="fps=1/3", qscale=2, f="image2", hide_banner=None, loglevel="error")
                .run(capture_stdout=True, capture_stderr=True, cmd=[self.ffmpeg_executable_path]) # 显式指定FFmpeg路径，并作为列表传递
            )
            self.logger.info(f"FFmpeg 抽帧完成到: {temp_output_dir}")
        except ffmpeg.Error as e:
            self.logger.error(f"FFmpeg 抽帧失败 for {video_path.name}: {e.stderr.decode()}")
            shutil.rmtree(temp_output_dir, ignore_errors=True)
            return []
        except Exception as e:
            self.logger.error(f"处理视频 {video_path.name} 时发生未知错误: {e}")
            shutil.rmtree(temp_output_dir, ignore_errors=True)
            return []

        # 获取所有抽取的帧文件
        extracted_frames = sorted(list(temp_output_dir.glob("*.jpg")))
        if not extracted_frames:
            self.logger.warning(f"未从视频 {video_path.name} 抽取到任何帧。")
            shutil.rmtree(temp_output_dir, ignore_errors=True)
            return []
        
        # 视频总时长和帧率用于估算每帧的时间
        # 使用ffprobe获取视频时长会更精确，但此处为了避免增加额外依赖或多次subprocess调用，
        # 暂沿用opencv的cap，或者可以完全移除，直接依赖srt文件的时间戳
        # 但如果srt文件不完整，cap还是有必要获取视频总时长以进行合理估算
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            self.logger.error(f"无法打开视频 {video_path} 来获取元数据。")
            shutil.rmtree(temp_output_dir, ignore_errors=True)
            return []
        total_frames_cv2 = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps_cv2 = cap.get(cv2.CAP_PROP_FPS)
        cap.release()

        gps_data = self.extract_gps_from_srt(str(srt_path)) if srt_path and srt_path.exists() else []
        
        frames_info = []
        for i, frame_path in enumerate(extracted_frames):
            # 估算抽帧的时间点 (近似值)
            current_time = i * 3.0 # 每3秒抽取一帧

            current_gps = self._get_gps_at_time(gps_data, current_time)
            
            frames_info.append({
                "image_path": str(frame_path),
                "original_source": str(video_path),
                "source_type": "video_frame",
                "frame_number": i, # 这里的frame_number是FFmpeg输出的序号，不是原始视频帧号
                "frame_time": current_time,
                "gps_info": current_gps
            })
        
        self.logger.info(f"成功从 {video_path.name} 抽取 {len(frames_info)} 帧")
        return frames_info

    def _create_sahi_slices(self, image_path: str, original_info: Dict) -> List[Dict]:
        """创建SAHI切片"""
        image = cv2.imread(image_path)
        if image is None:
            self.logger.error(f"无法读取图像进行切片: {image_path}")
            return []

        h, w = image.shape[:2]
        slices_data = []

        # 如果图像小于切片尺寸，直接作为单一切片处理
        if h <= self.slice_size and w <= self.slice_size:
            slices_data.append({
                "slice_path": image_path,
                "original_image_path": original_info.get("original_image_path", image_path),
                "original_source": original_info.get("original_source", image_path),
                "source_type": original_info.get("source_type", "image"),
                "frame_number": original_info.get("frame_number"),
                "frame_time": original_info.get("frame_time"),
                "gps_info": original_info.get("gps_info", self.extract_gps_from_exif(image_path)),
                "offset_x": 0,
                "offset_y": 0,
                "original_width": w,
                "original_height": h
            })
            return slices_data

        # 调整重叠率，这里使用20%重叠
        stride = self.slice_size * 4 // 5 
        
        for y in range(0, h, stride):
            for x in range(0, w, stride):
                y_end = min(y + self.slice_size, h)
                x_end = min(x + self.slice_size, w)
                
                # 确保切片不小于一半的切片尺寸，避免过小的边界切片
                if (y_end - y) < self.slice_size / 2 and y_end < h:
                    continue
                if (x_end - x) < self.slice_size / 2 and x_end < w:
                    continue
                
                slice_img = image[y:y_end, x:x_end]
                temp_slice_path = self.temp_dir / f"slice_{uuid.uuid4().hex}.jpg"
                cv2.imwrite(str(temp_slice_path), slice_img)

                slices_data.append({
                    "slice_path": str(temp_slice_path),
                    "original_image_path": original_info.get("original_image_path", image_path),
                    "original_source": original_info.get("original_source", image_path),
                    "source_type": original_info.get("source_type", "image"),
                    "frame_number": original_info.get("frame_number"),
                    "frame_time": original_info.get("frame_time"),
                    "gps_info": original_info.get("gps_info", self.extract_gps_from_exif(image_path)),
                    "offset_x": x,
                    "offset_y": y,
                    "original_width": w,
                    "original_height": h
                })
        return slices_data

    def load_and_preprocess(self, folder_paths: List[Path]) -> List[Dict]:
        """
        加载并预处理数据
        返回包含所有切片信息的列表
        """
        all_slices_info = []
        
        self.logger.info(f"数据加载和预处理阶段：发现 {len(folder_paths)} 个文件夹待处理，使用 {self.max_workers} 个工作线程")

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_folder = {}
            for folder_path in folder_paths:
                # 收集文件
                image_files = []
                video_files = []
                for file_path in folder_path.rglob("*"):
                    if file_path.is_file():
                        ext = file_path.suffix.lower()
                        if ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                            image_files.append(file_path)
                        elif ext in ['.mp4', '.avi', '.mov', '.mkv']:
                            video_files.append(file_path)
                
                self.logger.info(f"文件夹 '{folder_path.name}': 找到 {len(image_files)} 张图片和 {len(video_files)} 个视频")

                # 提交图片处理任务
                for img_file in image_files:
                    future_to_folder[executor.submit(self._create_sahi_slices, str(img_file), {"original_image_path": str(img_file), "source_type": "image"})] = str(img_file)
                
                # 提交视频处理任务
                for vid_file in video_files:
                    srt_file = vid_file.with_suffix('.srt')
                    if not srt_file.exists():
                        srt_file = vid_file.with_suffix('.SRT')
                    future_to_folder[executor.submit(self._process_video_for_frames, vid_file, srt_file)] = str(vid_file)
            
            # 收集所有处理结果
            for future in tqdm(as_completed(future_to_folder), total=len(future_to_folder), desc="处理文件和生成切片"):
                source_path = future_to_folder[future]
                try:
                    results = future.result()
                    if isinstance(results, list): # results can be list of frames_info or list of slices_data
                        for item in results:
                            if item["source_type"] == "video_frame":
                                # Need to create slices for each video frame
                                all_slices_info.extend(self._create_sahi_slices(item["image_path"], item))
                            else: # image slices
                                all_slices_info.extend(results)
                                break # only extend once for image slices as results are already slices
                    self.logger.info(f"完成预处理: {Path(source_path).name}")
                except Exception as e:
                    self.logger.error(f"预处理文件失败 {source_path}: {e}")
        
        # 移除处理完的临时视频帧文件 (SAHI切片后不再需要)
        for slice_info in all_slices_info:
            if slice_info["source_type"] == "video_frame":
                temp_frame_path = Path(slice_info["original_image_path"])
                if temp_frame_path.exists():
                    temp_frame_path.unlink(missing_ok=True) # 删除临时的视频帧文件
        
        self.logger.info(f"数据准备阶段完成，总共生成 {len(all_slices_info)} 个待检测切片")
        return all_slices_info

class Detector:
    """
    模型检测模块
    负责模型加载、批量推理和NMS
    """
    def __init__(self, model_path: str, confidence_threshold: float = 0.5, logger: logging.Logger = None, temp_dir: Path = None):
        self.model_path = model_path
        self.confidence_threshold = confidence_threshold
        self.logger = logger or logging.getLogger(__name__)
        self.temp_dir = temp_dir
        self.model = None
        self.device = "cpu"
        self.batch_size = 1 # 默认值，会被autobatch更新

        self._load_model()

    def _load_model(self):
        """加载TensorRT模型并优化配置"""
        try:
            self.logger.info(f"正在加载TensorRT模型: {self.model_path}")
            self.model = YOLO(self.model_path, task="detect")
            
            if torch.cuda.is_available():
                self.device = "cuda"
                self.logger.info(f"使用GPU: {torch.cuda.get_device_name(0)}")
                torch.backends.cudnn.benchmark = True
                torch.backends.cudnn.deterministic = False
                torch.cuda.empty_cache()
                torch.cuda.set_per_process_memory_fraction(0.99) # 使用99%显存
                self.batch_size = self._determine_optimal_batch_size()
                self.logger.info(f"自动确定的最优批处理大小: {self.batch_size}")
                self._warmup_gpu()
            else:
                self.device = "cpu"
                self.batch_size = 64 # CPU批处理大小可以更大
                self.logger.info("使用CPU")
                
            self.logger.info("TensorRT模型加载成功")
        except Exception as e:
            self.logger.error(f"TensorRT模型加载失败: {e}")
            raise

    def _determine_optimal_batch_size(self):
        """使用autobatch确定最优批处理大小"""
        try:
            self.logger.info("正在确定最优批处理大小...")
            test_img = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
            test_path = self.temp_dir / "autobatch_test.jpg"
            cv2.imwrite(str(test_path), test_img)
            
            # autobatch() 默认从1开始测试，stop=128，step=1
            optimal_batch = autobatch(self.model) # 移除 device 参数
            
            test_path.unlink(missing_ok=True)
            optimal_batch = max(1, min(optimal_batch, 256)) # 允许更大的批处理
            return optimal_batch
        except Exception as e:
            self.logger.warning(f"自动批处理大小确定失败: {e}，使用默认值128")
            return 128

    def _warmup_gpu(self):
        """预热GPU"""
        try:
            self.logger.info("预热GPU...")
            dummy_img = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
            dummy_path = self.temp_dir / "warmup.jpg"
            cv2.imwrite(str(dummy_path), dummy_img)
            for _ in range(30): # 增加预热次数到30
                _ = self.model(
                    str(dummy_path), 
                    conf=self.confidence_threshold, 
                    device=self.device, 
                    verbose=False,
                    stream=False
                )
            dummy_path.unlink(missing_ok=True)
            torch.cuda.empty_cache()
            self.logger.info("GPU预热完成")
        except Exception as e:
            self.logger.warning(f"GPU预热失败: {e}")

    def _non_max_suppression(self, detections: List[Dict], iou_threshold: float = 0.5) -> List[Dict]:
        """非极大值抑制去重"""
        if not detections:
            return []
        detections = sorted(detections, key=lambda x: x['confidence'], reverse=True)
        kept_detections = []
        for detection in detections:
            should_keep = True
            for kept in kept_detections:
                x1_i = max(detection['bbox_x1'], kept['bbox_x1'])
                y1_i = max(detection['bbox_y1'], kept['bbox_y1'])
                x2_i = min(detection['bbox_x2'], kept['bbox_x2'])
                y2_i = min(detection['bbox_y2'], kept['bbox_y2'])
                
                inter_w = max(0, x2_i - x1_i)
                inter_h = max(0, y2_i - y1_i)
                intersection = inter_w * inter_h

                area1 = (detection['bbox_x2'] - detection['bbox_x1']) * (detection['bbox_y2'] - detection['bbox_y1'])
                area2 = (kept['bbox_x2'] - kept['bbox_x1']) * (kept['bbox_y2'] - kept['bbox_y1'])
                union = area1 + area2 - intersection
                
                iou = intersection / union if union > 0 else 0.0
                if iou > iou_threshold:
                    should_keep = False
                    break
            if should_keep:
                kept_detections.append(detection)
        return kept_detections

    def detect(self, slices_info: List[Dict], memory_monitor: MemoryMonitor) -> List[Dict]:
        """
        执行批量检测
        """
        all_final_detections = []
        slice_paths_batch = []
        original_info_batch = []

        self.logger.info(f"模型检测阶段：共 {len(slices_info)} 个切片待检测，批处理大小为 {self.batch_size}")
        
        # 确保按原始文件分组处理，这样在NMS时可以在同一张原图内进行
        # 实际操作中，最好是分文件夹处理后，再将结果汇聚并进行全局NMS或者分图NMS
        # 这里为了简化，假设slices_info是打平的，NMS在最后进行
        
        for i, slice_info in enumerate(tqdm(slices_info, desc="检测切片", leave=False)):
            slice_paths_batch.append(slice_info["slice_path"])
            original_info_batch.append(slice_info)

            if len(slice_paths_batch) >= self.batch_size or i == len(slices_info) - 1:
                if not memory_monitor.check_memory():
                    self.logger.warning("内存使用过高，清理缓存后继续")
                    gc.collect()
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()

                try:
                    # 使用TensorRT模型进行批量检测
                    results = self.model(
                        slice_paths_batch, 
                        conf=self.confidence_threshold, 
                        device=self.device, 
                        verbose=False,
                        stream=False,
                        half=True,
                        agnostic_nms=True,
                        max_det=3000
                    )

                    for j, result in enumerate(results):
                        detections_in_slice = []
                        boxes = result.boxes
                        if boxes is not None:
                            for box in boxes:
                                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                                confidence = box.conf[0].cpu().numpy()
                                class_id = int(box.cls[0].cpu().numpy())
                                
                                current_original_info = original_info_batch[j]
                                offset_x = current_original_info['offset_x']
                                offset_y = current_original_info['offset_y']

                                detection_info = {
                                    'bbox_x1': float(x1 + offset_x),
                                    'bbox_y1': float(y1 + offset_y),
                                    'bbox_x2': float(x2 + offset_x),
                                    'bbox_y2': float(y2 + offset_y),
                                    'confidence': float(confidence),
                                    'class_id': class_id,
                                    'class_name': 'finless_porpoise', # 假设只有一个类别
                                    'detection_method': 'TensorRT_Modular',
                                    'original_image_path': current_original_info['original_image_path'],
                                    'original_source': current_original_info['original_source'],
                                    'source_type': current_original_info['source_type'],
                                    'frame_number': current_original_info.get('frame_number'),
                                    'frame_time': current_original_info.get('frame_time'),
                                    'gps_info': current_original_info.get('gps_info', {})
                                }
                                detections_in_slice.append(detection_info)
                        all_final_detections.extend(detections_in_slice)
                except Exception as e:
                    self.logger.error(f"批量检测切片失败: {e}")

                # 清理已处理的临时切片文件
                for slice_path in slice_paths_batch:
                    Path(slice_path).unlink(missing_ok=True)
                
                slice_paths_batch = []
                original_info_batch = []
                gc.collect()
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

        self.logger.info(f"模型检测阶段完成，共检测到 {len(all_final_detections)} 个原始目标 (包含SAHI重复)")
        # 对所有检测结果进行NMS，因为我们是针对切片进行的检测，最终需要合并
        final_detections_after_nms = self._non_max_suppression(all_final_detections)
        self.logger.info(f"NMS后，最终检测到 {len(final_detections_after_nms)} 个目标")
        return final_detections_after_nms

class ReportGenerator:
    """
    报告生成模块
    负责保存结果、生成统计报告和可视化图表
    """
    def __init__(self, results_dir: Path, logger: logging.Logger = None):
        self.results_dir = results_dir
        self.results_dir.mkdir(exist_ok=True)
        self.logger = logger or logging.getLogger(__name__)

    def save_results(self, detections: List[Dict], folder_name: str):
        """保存检测结果到CSV和JSON文件"""
        folder_results_dir = self.results_dir / folder_name
        folder_results_dir.mkdir(exist_ok=True)

        csv_file = folder_results_dir / f"{folder_name}_detections.csv"
        if detections:
            df = pd.DataFrame(detections)
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            self.logger.info(f"检测结果已保存到: {csv_file}")
        else:
            self.logger.info(f"文件夹 {folder_name} 无检测结果，跳过CSV保存")

        json_file = folder_results_dir / f"{folder_name}_detections.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(detections, f, ensure_ascii=False, indent=2)
        self.logger.info(f"检测结果已保存到: {json_file}")

    def generate_report(self, detections: List[Dict], folder_name: str, batch_size: int, max_workers: int, slice_size: int, device: str, max_memory_gb: int):
        """生成详细的检测报告"""
        folder_results_dir = self.results_dir / folder_name
        
        if not detections:
            report = f"""
# 铜陵无人机检测报告 - {folder_name} (模块化终极优化版本)

## 检测统计
- 总检测数量: 0
- 平均置信度: N/A
- 最高置信度: N/A
- 最低置信度: N/A

## 检测方法
- 模块化终极优化TensorRT推理引擎
- 自动批处理大小优化 (大小: {batch_size})
- 批量SAHI切片检测 (尺寸: {slice_size})
- 多线程并行处理 ({max_workers} 线程)
- FP16加速推理
- 非极大值抑制去重

## 性能信息
- 模型: best.engine (TensorRT)
- 推理框架: ultralytics + TensorRT
- 硬件加速: {device.upper()}
- 批处理大小: {batch_size} (自动优化)
- 最大内存使用: {max_memory_gb}GB
"""
            report_file = folder_results_dir / f"{folder_name}_report.md"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            self.logger.info(f"为 {folder_name} 生成了无检测结果的报告")
            return

        total_detections = len(detections)
        confidence_scores = [d['confidence'] for d in detections]
        avg_confidence = np.mean(confidence_scores)
        max_confidence = np.max(confidence_scores)
        min_confidence = np.min(confidence_scores)

        report = f"""
# 铜陵无人机检测报告 - {folder_name} (模块化终极优化版本)

## 检测统计
- 总检测数量: {total_detections}
- 平均置信度: {avg_confidence:.3f}
- 最高置信度: {max_confidence:.3f}
- 最低置信度: {min_confidence:.3f}

## 检测方法
- 模块化终极优化TensorRT推理引擎
- 自动批处理大小优化 (大小: {batch_size})
- 批量SAHI切片检测 (尺寸: {slice_size})
- 多线程并行处理 ({max_workers} 线程)
- FP16加速推理
- 非极大值抑制去重

## 性能信息
- 模型: best.engine (TensorRT)
- 推理框架: ultralytics + TensorRT
- 硬件加速: {device.upper()}
- 批处理大小: {batch_size} (自动优化)
- 最大内存使用: {max_memory_gb}GB
"""
        report_file = folder_results_dir / f"{folder_name}_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        self.logger.info(f"检测报告已保存到: {report_file}")

        self.generate_visualizations(detections, folder_results_dir, folder_name)

    def generate_visualizations(self, detections: List[Dict], results_dir: Path, folder_name: str):
        """生成可视化图表"""
        if not detections:
            return

        # 置信度分布图
        plt.figure(figsize=(10, 6))
        confidence_scores = [d['confidence'] for d in detections]
        plt.hist(confidence_scores, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        plt.xlabel('置信度')
        plt.ylabel('检测数量')
        plt.title(f'{folder_name} - 终极优化检测置信度分布')
        plt.grid(True, alpha=0.3)
        plt.savefig(results_dir / f'{folder_name}_confidence_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        self.logger.info(f"生成置信度分布图: {results_dir / f'{folder_name}_confidence_distribution.png'}")

        # GPS分布图（如果有GPS信息）
        gps_detections = [d for d in detections if 'latitude' in d.get('gps_info', {}) and 'longitude' in d.get('gps_info', {})]
        if gps_detections:
            plt.figure(figsize=(12, 8))
            lats = [d['gps_info']['latitude'] for d in gps_detections]
            lons = [d['gps_info']['longitude'] for d in gps_detections]
            confidences = [d['confidence'] for d in gps_detections]
            
            scatter = plt.scatter(lons, lats, c=confidences, cmap='viridis', s=50, alpha=0.7)
            plt.colorbar(scatter, label='置信度')
            plt.xlabel('经度')
            plt.ylabel('纬度')
            plt.title(f'{folder_name} - 终极优化检测目标GPS分布')
            plt.grid(True, alpha=0.3)
            plt.savefig(results_dir / f'{folder_name}_gps_distribution.png', dpi=300, bbox_inches='tight')
            plt.close()
            self.logger.info(f"生成GPS分布图: {results_dir / f'{folder_name}_gps_distribution.png'}")
        else:
            self.logger.info(f"文件夹 {folder_name} 无GPS信息，跳过GPS分布图生成")

    def generate_summary_report(self, all_results: Dict[str, List[Dict]], total_folders: int, detector_params: Dict):
        """生成总体报告"""
        summary_file = self.results_dir / "总体检测报告_模块化终极优化版本.md"
        
        total_detections_across_all_folders = sum(len(detections) for detections in all_results.values())
        
        summary = f"""
# 铜陵无人机检测总体报告 - 模块化终极优化版本

## 检测统计
- 总文件夹数: {total_folders}
- 总检测数量: {total_detections_across_all_folders}
- 推理引擎: 模块化终极优化TensorRT + 自动批处理优化
- 模型文件: {detector_params['model_path']}
- 设备: {detector_params['device'].upper()}
- 数据加载/预处理线程数: {detector_params['data_loader_max_workers']}
- SAHI切片尺寸: {detector_params['slice_size']}
- 检测器批处理大小: {detector_params['batch_size']} (自动优化)
- 最大内存使用: {detector_params['max_memory_gb']}GB

## 各文件夹检测结果
"""
        for folder_name, detections in all_results.items():
            if detections:
                avg_confidence = np.mean([d['confidence'] for d in detections])
                summary += f"- {folder_name}: {len(detections)} 个检测，平均置信度 {avg_confidence:.3f}\n"
            else:
                summary += f"- {folder_name}: 无检测结果\n"
                
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary)
        self.logger.info(f"总体报告已保存到: {summary_file}")

class TonglingModularOptimizedDetector:
    def __init__(self, model_path="best.engine", confidence_threshold=0.5, 
                 slice_size=1280, data_loader_max_workers=None, detector_max_workers=None, max_memory_gb=200, ffmpeg_executable_path: Optional[str] = None):
        self.model_path = model_path
        self.confidence_threshold = confidence_threshold
        self.slice_size = slice_size
        self.max_memory_gb = max_memory_gb
        
        self.results_dir = Path("tongling_results_modular_ultimate_optimized")
        self.results_dir.mkdir(exist_ok=True)
        
        # 使用一个共享的临时目录
        self.shared_temp_dir = Path(tempfile.mkdtemp(prefix="tongling_modular_all_"))
        
        self.setup_logging()
        self.memory_monitor = MemoryMonitor(max_memory_gb)
        
        # 动态添加 FFmpeg 路径到 PATH 环境变量 (此逻辑不再需要，因为直接指定 cmd)
        # if ffmpeg_executable_path and os.path.exists(ffmpeg_executable_path):
        #     ffmpeg_bin_dir = str(Path(ffmpeg_executable_path).parent)
        #     if ffmpeg_bin_dir not in os.environ['PATH'].split(os.pathsep):
        #         os.environ['PATH'] = ffmpeg_bin_dir + os.pathsep + os.environ['PATH']
        #         self.logger.info(f"已将 FFmpeg 路径添加到当前进程的 PATH: {ffmpeg_bin_dir}")
        # else:
        #     self.logger.warning(f"未提供有效的 FFmpeg 可执行文件路径或文件不存在: {ffmpeg_executable_path}")

        self.data_loader = DataLoader(
            base_path="", # 实际路径在process_all_folders中传入
            slice_size=slice_size, 
            max_workers=data_loader_max_workers, 
            temp_dir=self.shared_temp_dir, 
            logger=self.logger,
            ffmpeg_executable_path=ffmpeg_executable_path # 传递 FFmpeg 路径
        )
        self.detector = Detector(
            model_path=model_path, 
            confidence_threshold=confidence_threshold, 
            logger=self.logger, 
            temp_dir=self.shared_temp_dir
        )
        self.report_generator = ReportGenerator(
            results_dir=self.results_dir, 
            logger=self.logger
        )
        
        self.detector_params = {
            "model_path": model_path,
            "confidence_threshold": confidence_threshold,
            "slice_size": slice_size,
            "data_loader_max_workers": self.data_loader.max_workers,
            "detector_max_workers": "N/A (批处理由autobatch决定)", # 修正这里，Detector不直接使用max_workers
            "batch_size": self.detector.batch_size, # 实际的GPU批处理大小
            "max_memory_gb": max_memory_gb,
            "device": self.detector.device
        }

    def setup_logging(self):
        log_file = self.results_dir / f"detection_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('TonglingModularOptimizedDetector')

    def process_all_folders(self, base_path: str, limit_folders: Optional[int] = None):
        base_path = Path(base_path)
        self.logger.info(f"开始处理所有文件夹: {base_path}")
        
        subfolders = [f for f in base_path.iterdir() if f.is_dir()]
        
        if limit_folders:
            subfolders = subfolders[:limit_folders]
            self.logger.info(f"限制处理前 {limit_folders} 个子文件夹")

        self.logger.info(f"找到 {len(subfolders)} 个子文件夹待处理")
        
        all_results_by_folder = {}

        for folder in subfolders:
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"开始处理文件夹: {folder.name}")
            self.logger.info(f"{'='*60}")

            # 阶段1: 数据准备
            self.logger.info(f"[阶段1] 开始数据准备和预处理 for {folder.name}")
            # 这里的DataLoader需要更新base_path以处理当前子文件夹
            self.data_loader.base_path = folder
            slices_info = self.data_loader.load_and_preprocess([folder]) # 传入当前子文件夹
            self.logger.info(f"[阶段1] 数据准备完成 for {folder.name}，共 {len(slices_info)} 个切片")
            
            # 阶段2: 模型检测
            self.logger.info(f"[阶段2] 开始模型检测 for {folder.name}")
            folder_detections = self.detector.detect(slices_info, self.memory_monitor)
            self.logger.info(f"[阶段2] 模型检测完成 for {folder.name}，检测到 {len(folder_detections)} 个目标")
            
            all_results_by_folder[folder.name] = folder_detections

            # 阶段3: 报告生成
            self.logger.info(f"[阶段3] 开始生成报告 for {folder.name}")
            self.report_generator.save_results(folder_detections, folder.name)
            self.report_generator.generate_report(
                folder_detections, 
                folder.name, 
                self.detector.batch_size, 
                self.data_loader.max_workers, # 这里是DataLoader的max_workers, 实际的max_workers是self.max_workers
                self.slice_size, 
                self.detector.device, 
                self.max_memory_gb
            )
            self.logger.info(f"[阶段3] 报告生成完成 for {folder.name}")
            
            # 清理当前文件夹产生的临时SAHI切片文件
            # 注意: video_frame的临时文件在DataLoader.load_and_preprocess中已经清理
            for s_info in slices_info:
                if Path(s_info["slice_path"]).exists() and Path(s_info["slice_path"]) != Path(s_info["original_image_path"]):
                    Path(s_info["slice_path"]).unlink(missing_ok=True) # 删除切片文件

            self.memory_monitor.log_memory_usage() # 每次处理完一个文件夹，记录内存使用

        # 生成总体报告
        self.report_generator.generate_summary_report(
            all_results_by_folder, 
            len(subfolders), 
            {
                "model_path": self.model_path,
                "data_loader_max_workers": self.data_loader.max_workers,
                "slice_size": self.slice_size,
                "batch_size": self.detector.batch_size,
                "max_memory_gb": self.max_memory_gb,
                "device": self.detector.device
            }
        )
        
        # 清理所有共享的临时目录
        import shutil
        shutil.rmtree(self.shared_temp_dir, ignore_errors=True)
        self.logger.info(f"所有临时文件已清理: {self.shared_temp_dir}")
        self.logger.info("🎉 模块化终极优化检测完成！")


def main():
    print("🐋 铜陵无人机数据检测系统 - 模块化终极优化版本")
    print("="*50)
    
    model_path = "best.engine"
    if not os.path.exists(model_path):
        print(f"❌ TensorRT模型文件不存在: {model_path}")
        return
        
    data_path = "20250718铜陵"
    if not os.path.exists(data_path):
        print(f"❌ 数据文件夹不存在: {data_path}")
        return

    # !!! 请在这里配置你的FFmpeg可执行文件完整路径 !!!
    # 例如: r"C:\ffmpeg\bin\ffmpeg.exe" 或者 r"D:\your_ffmpeg_path\ffmpeg.exe"
    ffmpeg_executable_path = r"D:\anfh_workspace\ffmpeg-2025-07-28-git-dc8e753f32-essentials_build\bin\ffmpeg.exe"
    
    # 检查FFmpeg路径是否存在
    if not os.path.exists(ffmpeg_executable_path):
        print(f"❌ FFmpeg 可执行文件不存在: {ffmpeg_executable_path}")
        print("请检查路径是否正确，或安装 FFmpeg 并更新此路径。")
        return
        
    detector_app = TonglingModularOptimizedDetector(
        model_path=model_path,
        confidence_threshold=0.5,
        slice_size=1280,
        data_loader_max_workers=64, # CPU核心数
        # detector_max_workers is not directly used for batching in Detector class, 
        # but for ThreadPoolExecutor for file processing in DataLoader.
        # Detector uses its own batch_size for model inference.
        max_memory_gb=200,
        ffmpeg_executable_path=ffmpeg_executable_path # 传递 FFmpeg 路径
    )
    
    # 限制只处理前两条航线
    detector_app.process_all_folders(data_path, limit_folders=2)
    
    print("🎉 模块化终极优化检测完成！结果保存在 tongling_results_modular_ultimate_optimized 文件夹中")

if __name__ == "__main__":
    main()