@tailwind base;
@tailwind components;
@tailwind utilities;

/* 海洋主题动画效果 */
@layer utilities {
  .wave-animation {
    position: relative;
    overflow: hidden;
  }
  
  .wave-animation::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: wave 2s infinite;
  }
  
  .bubble-field {
    position: relative;
  }
  
  .bubble {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 40%, rgba(255,255,255,0.8), rgba(56,189,248,0.4));
    animation: bubble 4s infinite ease-in-out;
  }
  
  .bubble:nth-child(1) { left: 10%; animation-delay: 0s; }
  .bubble:nth-child(2) { left: 20%; animation-delay: 0.5s; }
  .bubble:nth-child(3) { left: 35%; animation-delay: 1s; }
  .bubble:nth-child(4) { left: 50%; animation-delay: 1.5s; }
  .bubble:nth-child(5) { left: 70%; animation-delay: 2s; }
  .bubble:nth-child(6) { left: 85%; animation-delay: 2.5s; }
}

/* 海豚可爱的动画 */
.porpoise-swim {
  animation: float 3s ease-in-out infinite;
}

/* 进度条样式 */
.progress-bar {
  background: linear-gradient(90deg, #0ea5e9, #38bdf8, #0ea5e9);
  background-size: 200% 200%;
  animation: progress 2s ease-in-out infinite;
}

/* 卡片阴影效果 */
.ocean-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(14, 165, 233, 0.1);
} 