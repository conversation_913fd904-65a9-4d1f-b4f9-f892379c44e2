'use client'

import { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Upload, Zap, Settings, Download, Eye } from 'lucide-react'
import FileUpload from './components/FileUpload'
import ProgressTracker from './components/ProgressTracker'
import ResultsDisplay from './components/ResultsDisplay'
import PorpoiseIllustration from './components/PorpoiseIllustration'
import SettingsPanel from './components/SettingsPanel'

export default function Home() {
  const [files, setFiles] = useState<File[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [progress, setProgress] = useState({
    currentStep: 0,
    totalSteps: 0,
    currentFile: '',
    message: ''
  })
  const [results, setResults] = useState(null)
  const [settings, setSettings] = useState({
    confidence: 0.25,
    useSahi: false,
    sliceSize: 1280
  })

  const handleUpload = useCallback((uploadedFiles: File[]) => {
    setFiles(uploadedFiles)
    setResults(null)
  }, [])

  const handleDetection = useCallback(async () => {
    if (files.length === 0) return
    
    setIsProcessing(true)
    setProgress({
      currentStep: 0,
      totalSteps: files.length * 4, // 每个文件4个步骤
      currentFile: files[0].name,
      message: '正在初始化检测...'
    })

    try {
      const formData = new FormData()
      files.forEach(file => formData.append('files', file))
      formData.append('confidence', settings.confidence.toString())
      formData.append('use_sahi', settings.useSahi.toString())

      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev.currentStep < prev.totalSteps) {
            const stepNames = ['读取图片', '提取GPS', '执行检测', '生成结果']
            const currentFileIndex = Math.floor(prev.currentStep / 4)
            const stepIndex = prev.currentStep % 4
            
            return {
              ...prev,
              currentStep: prev.currentStep + 1,
              currentFile: files[currentFileIndex]?.name || '',
              message: `${stepNames[stepIndex]} - ${files[currentFileIndex]?.name || ''}`
            }
          }
          return prev
        })
      }, 800)

      const response = await fetch('/api/detect/upload', {
        method: 'POST',
        body: formData
      })

      clearInterval(progressInterval)
      
      if (response.ok) {
        const data = await response.json()
        setResults(data)
        setProgress(prev => ({
          ...prev,
          currentStep: prev.totalSteps,
          message: `检测完成！发现 ${data.total_detections} 个目标`
        }))
      } else {
        throw new Error('检测失败')
      }
    } catch (error) {
      console.error('Detection error:', error)
      setProgress(prev => ({
        ...prev,
        message: '检测过程中出现错误'
      }))
    } finally {
      setIsProcessing(false)
    }
  }, [files, settings])

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* 背景气泡动画 */}
      <div className="bubble-field fixed inset-0 pointer-events-none">
        {[...Array(6)].map((_, i) => (
          <div 
            key={i} 
            className="bubble w-4 h-4 opacity-30"
            style={{
              bottom: Math.random() * 20 + '%',
              animationDuration: `${3 + Math.random() * 2}s`,
              animationDelay: `${i * 0.5}s`
            }}
          />
        ))}
      </div>

      {/* 主容器 */}
      <div className="relative z-10 container mx-auto px-4 py-8">
        {/* 标题区域 */}
        <motion.div 
          className="text-center mb-12"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-center mb-6">
            <PorpoiseIllustration className="w-20 h-20 mr-4" />
            <div>
              <h1 className="text-4xl font-bold text-porpoise-800 mb-2">
                江豚检测系统
              </h1>
              <p className="text-ocean-600 text-lg">
                基于AI技术的海洋生物保护助手
              </p>
            </div>
          </div>
        </motion.div>

        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* 左侧：上传和设置 */}
          <motion.div 
            className="xl:col-span-1 space-y-6"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {/* 文件上传 */}
            <div className="ocean-card rounded-2xl p-6">
              <h2 className="text-xl font-semibold text-porpoise-800 mb-4 flex items-center">
                <Upload className="w-5 h-5 mr-2" />
                上传图片
              </h2>
              <FileUpload onUpload={handleUpload} />
              
              {files.length > 0 && (
                <motion.div 
                  className="mt-4 p-4 bg-ocean-50 rounded-lg"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                >
                  <p className="text-sm text-ocean-700">
                    已选择 {files.length} 张图片
                  </p>
                  <ul className="mt-2 space-y-1">
                    {files.slice(0, 3).map((file, index) => (
                      <li key={index} className="text-xs text-ocean-600 truncate">
                        {file.name}
                      </li>
                    ))}
                    {files.length > 3 && (
                      <li className="text-xs text-ocean-500">
                        还有 {files.length - 3} 张图片...
                      </li>
                    )}
                  </ul>
                </motion.div>
              )}
            </div>

            {/* 设置面板 */}
            <div className="ocean-card rounded-2xl p-6">
              <h2 className="text-xl font-semibold text-porpoise-800 mb-4 flex items-center">
                <Settings className="w-5 h-5 mr-2" />
                检测设置
              </h2>
              <SettingsPanel settings={settings} onSettingsChange={setSettings} />
            </div>

            {/* 开始检测按钮 */}
            <motion.button
              onClick={handleDetection}
              disabled={files.length === 0 || isProcessing}
              className={`w-full py-4 px-6 rounded-2xl font-semibold text-white transition-all duration-300 flex items-center justify-center ${
                files.length === 0 || isProcessing
                  ? 'bg-porpoise-300 cursor-not-allowed'
                  : 'bg-ocean-gradient hover:shadow-lg transform hover:scale-105'
              }`}
              whileHover={files.length > 0 && !isProcessing ? { scale: 1.02 } : {}}
              whileTap={files.length > 0 && !isProcessing ? { scale: 0.98 } : {}}
            >
              <Zap className="w-5 h-5 mr-2" />
              {isProcessing ? '检测中...' : '开始检测'}
            </motion.button>
          </motion.div>

          {/* 右侧：进度和结果 */}
          <motion.div 
            className="xl:col-span-2 space-y-6"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            {/* 进度追踪器 */}
            <AnimatePresence>
              {(isProcessing || results) && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="ocean-card rounded-2xl p-6"
                >
                  <h2 className="text-xl font-semibold text-porpoise-800 mb-4 flex items-center">
                    <Eye className="w-5 h-5 mr-2" />
                    检测进度
                  </h2>
                  <ProgressTracker 
                    progress={progress} 
                    isProcessing={isProcessing}
                  />
                </motion.div>
              )}
            </AnimatePresence>

            {/* 结果显示 */}
            <AnimatePresence>
              {results && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="ocean-card rounded-2xl p-6"
                >
                  <h2 className="text-xl font-semibold text-porpoise-800 mb-4 flex items-center">
                    <Download className="w-5 h-5 mr-2" />
                    检测结果
                  </h2>
                  <ResultsDisplay results={results} />
                </motion.div>
              )}
            </AnimatePresence>

            {/* 默认状态：可爱的插画 */}
            {!isProcessing && !results && (
              <motion.div 
                className="ocean-card rounded-2xl p-12 text-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
              >
                <PorpoiseIllustration className="w-48 h-48 mx-auto mb-6" animated />
                <h3 className="text-2xl font-semibold text-porpoise-700 mb-2">
                  准备就绪！
                </h3>
                <p className="text-porpoise-500">
                  上传您的航拍图片，让AI帮您寻找可爱的江豚朋友们
                </p>
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  )
} 