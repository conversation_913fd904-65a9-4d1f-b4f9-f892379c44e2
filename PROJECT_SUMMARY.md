# 江豚检测系统项目总结

## 项目概述

江豚检测系统是一个基于YOLOv11和SAHI技术的江豚检测与GPS定位系统。该系统可以从图像中检测江豚，并结合GPS信息进行定位，帮助研究人员更好地监测和保护江豚种群。

## 系统架构

系统由以下几个主要部分组成：

1. **检测器核心模块** (`src/detector.py`)
   - 基于YOLOv11的目标检测
   - SAHI切片检测技术，提高小目标检测能力
   - GPS信息提取与坐标转换

2. **Web应用界面**
   - 标准版 (`web_app.py`)：基本的Flask Web界面
   - 增强版 (`enhanced_web_app.py`)：支持WebSocket实时进度更新和现代化API接口

3. **辅助工具**
   - 依赖修复工具 (`fix_dependencies.py`)
   - 系统状态检查 (`system_status.py`)
   - 检测器测试工具 (`test_detector.py`)
   - 一键式启动脚本 (`run.py`)

## 安装与设置

### 系统要求

- Python 3.8+
- PyTorch 2.0+
- CUDA支持（可选，用于GPU加速）
- 至少4GB RAM
- 至少2GB磁盘空间

### 安装步骤

1. 克隆或下载项目代码
2. 运行依赖修复工具：
   ```
   python fix_dependencies.py
   ```
3. 确保`best.pt`模型文件放置在项目根目录

## 使用方法

### 一键式启动（推荐）

使用一键式启动脚本可以自动完成系统检查、依赖修复和启动：

```
python run.py
```

### 手动启动

1. 检查系统状态：
   ```
   python system_status.py
   ```

2. 修复依赖（如需要）：
   ```
   python fix_dependencies.py
   ```

3. 测试检测器：
   ```
   python test_detector.py
   ```

4. 启动Web应用：
   ```
   python web_app.py  # 标准版
   ```
   或
   ```
   python enhanced_web_app.py  # 增强版
   ```

## Web应用使用指南

1. 启动Web应用后，在浏览器中访问：`http://localhost:5000`
2. 上传图像文件（支持JPG、PNG格式）
3. 调整检测参数（置信度阈值、是否使用SAHI等）
4. 点击"开始检测"按钮
5. 查看检测结果，包括：
   - 标注后的图像
   - 检测到的江豚数量和位置
   - GPS坐标信息（如果图像包含EXIF数据）

## 技术细节

### 检测器原理

1. **YOLOv11检测**：使用最新的YOLOv11模型进行目标检测，具有高精度和快速推理能力
2. **SAHI切片检测**：将大图像切分为小块进行检测，然后合并结果，提高小目标检测能力
3. **GPS定位**：从图像EXIF信息中提取GPS数据，结合相机参数计算目标的实际地理位置

### 依赖管理

系统使用了以下主要依赖：

- `ultralytics`: YOLOv11模型实现
- `sahi`: 切片检测技术
- `torch`和`torchvision`: 深度学习框架
- `opencv-python`: 图像处理
- `flask`: Web应用框架
- `exifread`和`piexif`: EXIF信息处理
- `utm`和`geopy`: GPS坐标转换和计算

## 故障排除

### 常见问题

1. **模型加载失败**
   - 确保`best.pt`文件存在且未损坏
   - 检查是否正确安装了ultralytics

2. **依赖问题**
   - 运行`fix_dependencies.py`修复依赖
   - 检查Python版本是否兼容

3. **检测效果不佳**
   - 调整置信度阈值
   - 对于小目标，启用SAHI切片检测
   - 确保图像质量良好

### 日志文件

系统会自动生成检测日志文件（`detection_log_*.log`）和系统状态报告（`system_status_*.json`），可用于诊断问题。

## 未来改进

1. 增加视频处理功能
2. 改进用户界面，提供更多可视化选项
3. 添加批量处理功能
4. 集成更多模型选择
5. 支持在线模型更新

## 联系方式

如有问题或建议，请联系项目维护者。 