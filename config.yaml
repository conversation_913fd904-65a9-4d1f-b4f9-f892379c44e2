# 江豚检测系统配置文件

# 模型配置
model:
  weights_path: "best.pt"  # 训练好的模型权重路径
  confidence_threshold: 0.25  # 置信度阈值
  iou_threshold: 0.45  # NMS IoU阈值
  device: "auto"  # 设备选择: auto, cpu, cuda, mps

# SAHI切片检测配置
sahi:
  enabled: true  # 是否启用SAHI切片检测
  slice_height: 640  # 切片高度 (减小以提高性能)
  slice_width: 640   # 切片宽度 (减小以提高性能)
  overlap_height_ratio: 0.1  # 垂直重叠比例 (减小以提高性能)
  overlap_width_ratio: 0.1   # 水平重叠比例 (减小以提高性能)
  postprocess_type: "NMS"    # 后处理类型
  postprocess_match_metric: "IOU"  # 匹配度量
  postprocess_match_threshold: 0.5  # 匹配阈值

# DJI Mavic 3 相机参数
mavic3_camera:
  sensor_width_mm: 17.3  # 传感器宽度 (mm)
  sensor_height_mm: 13.0  # 传感器高度 (mm)
  focal_length_mm: 24.0   # 焦距 (mm)
  image_width_px: 5280    # 图像宽度 (像素)
  image_height_px: 3956   # 图像高度 (像素)

# GPS计算配置
gps:
  altitude_offset: 0.0  # 高度偏移量 (米)
  coordinate_system: "WGS84"  # 坐标系统
  precision_decimal: 6  # GPS坐标精度（小数位数）

# 检测类别配置
classes:
  finless_porpoise: 0  # 江豚类别ID

# 输出配置
output:
  save_annotated_images: true  # 是否保存标注图像
  save_crops: true  # 是否保存裁剪图像
  csv_delimiter: ","  # CSV分隔符
  image_format: "jpg"  # 输出图像格式
  
# 批处理配置
batch_processing:
  max_workers: 4  # 最大并行处理数
  chunk_size: 10  # 批次大小

# 视频处理配置
video:
  extract_fps: 1  # 视频帧提取帧率（每秒提取帧数）
  supported_formats: [".mp4", ".avi", ".mov", ".mkv"]

# 日志配置
logging:
  level: "INFO"  # 日志级别: DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  save_to_file: true  # 是否保存到文件

# 可视化配置
visualization:
  bbox_color: [0, 255, 0]  # 边界框颜色 (BGR)
  bbox_thickness: 2  # 边界框线条粗细
  text_color: [255, 255, 255]  # 文本颜色 (BGR)
  text_scale: 0.7  # 文本大小
  show_confidence: true  # 是否显示置信度 