# 🐋 江豚检测系统

基于YOLOv11和SAHI技术的DJI Mavic 3航拍江豚自动检测与GPS定位系统

## 📋 项目概述

本系统专为无人机航拍场景下的江豚保护与科研应用而设计，实现了从数据采集、模型训练、批量检测到结果分析的全流程自动化。

### 🎯 核心功能

- **🔍 高精度检测**: 基于YOLOv11深度学习算法，结合SAHI切片技术提升小目标检测能力
- **📍 精准GPS定位**: 自动解析图片EXIF信息，计算每个检测目标的精确GPS坐标
- **🎥 多媒体支持**: 支持静态图片和视频的批量检测与处理
- **📊 详细报告**: 自动生成包含GPS坐标、置信度等信息的详细检测报告
- **🚁 设备优化**: 针对DJI Mavic 3设备参数专项优化
- **🎛️ 灵活配置**: 支持自定义参数配置和多种训练模式

### ✨ 技术特点

- **SAHI切片检测**: 1280×1280切片，提升小目标检测精度
- **GPS坐标计算**: 基于相机参数和飞行高度的精确定位算法
- **批量处理**: 支持大规模图片和视频的自动化处理
- **模型训练**: 完整的训练、验证、权重迁移流程
- **多种界面**: 命令行和图形界面双重支持

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Anaconda/Miniconda
- CUDA支持（推荐，用于GPU加速）

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-repo/finless-porpoise-detection.git
cd finless-porpoise-detection
```

2. **创建环境**
```bash
conda create -n yolov11 python=3.9
conda activate yolov11
```

3. **安装依赖**
```bash
# 基础依赖
pip install ultralytics sahi opencv-python pillow pandas

# GPS和EXIF处理
pip install exifread piexif geopy utm

# 可视化和分析
pip install matplotlib seaborn plotly

# 视频处理（可选）
pip install moviepy

# 其他工具
pip install tqdm pyyaml
```

或使用requirements.txt:
```bash
pip install -r requirements.txt
```

4. **放置模型权重**
```bash
# 将您的best.pt模型文件放在项目根目录
cp /path/to/your/best.pt ./
```

## 📖 使用指南

### 1. 配置系统

首次使用前，请检查并调整 `config.yaml` 配置文件：

```yaml
# 模型配置
model:
  weights_path: "best.pt"  # 您的模型路径
  confidence_threshold: 0.25
  iou_threshold: 0.45

# SAHI切片配置（已优化为1280）
sahi:
  enabled: true
  slice_height: 1280
  slice_width: 1280
  overlap_height_ratio: 0.2
  overlap_width_ratio: 0.2

# DJI Mavic 3相机参数
mavic3_camera:
  sensor_width_mm: 17.3
  sensor_height_mm: 13.0
  focal_length_mm: 24.0
  image_width_px: 5280
  image_height_px: 3956
```

### 2. 图片检测

#### 单张图片检测
```bash
python main.py detect -i photo.jpg
```

#### 批量检测
```bash
python main.py detect -i photos_directory/ -o results/
```

#### 使用SAHI切片检测
```bash
python main.py detect -i photos/ --sahi -o results_sahi/
```

### 3. 视频处理

#### 处理单个视频
```bash
python main.py video -v drone_video.mp4 -o video_results/
```

#### 批量处理视频
```bash
python main.py video -v videos_directory/ --batch -o batch_results/
```

#### 自定义帧提取率
```bash
python main.py video -v video.mp4 --fps 0.5 -o results/
```

### 4. 模型训练

#### 准备数据集
确保您的数据集结构如下：
```
datasets/
├── train/
│   ├── images/
│   └── labels/
├── val/
│   ├── images/
│   └── labels/
└── test/
    ├── images/
    └── labels/
```

#### 训练新模型
```bash
python main.py train -d datasets/ -m train
```

#### 验证现有模型
```bash
python main.py train -w best.pt -m validate
```

#### 权重迁移
```bash
python main.py train -w pretrained_model.pt -d new_dataset/ -m transfer
```

### 5. 图形界面

启动图形界面进行可视化操作：
```bash
python main.py gui
```

图形界面支持：
- 配置文件选择
- 输入输出路径设置
- 检测模式切换
- 实时日志显示
- 进度监控

## 📁 项目结构

```
jiangtun/
├── best.pt                 # 您的训练好的模型
├── config.yaml            # 系统配置文件
├── main.py                # 主程序入口
├── requirements.txt       # 依赖列表
├── README.md             # 项目说明
├── src/                  # 源代码目录
│   ├── __init__.py
│   ├── detector.py       # 核心检测器
│   ├── video_processor.py # 视频处理器
│   └── model_trainer.py  # 模型训练器
├── datasets/             # 数据集目录（可选）
├── runs/                 # 训练结果目录
└── logs/                 # 日志文件目录
```

## 📊 输出结果

### 检测报告 (CSV格式)

| 字段 | 描述 |
|------|------|
| image_path | 图片路径 |
| detection_id | 检测ID |
| class_name | 类别名称 (finless_porpoise) |
| confidence | 置信度 (0-1) |
| bbox_x1, bbox_y1, bbox_x2, bbox_y2 | 边界框坐标 |
| latitude, longitude | GPS坐标 |
| altitude | 飞行高度 |
| detection_method | 检测方法 (YOLO/SAHI) |
| timestamp | 检测时间 |

### 可视化结果

- **标注图片**: 带有边界框和置信度的检测结果图片
- **训练曲线**: 模型训练过程的损失和精度曲线
- **统计报告**: JSON格式的详细统计信息

## ⚙️ 高级配置

### DJI Mavic 3参数调整

如果您使用的是其他型号无人机，请在 `config.yaml` 中调整相机参数：

```yaml
mavic3_camera:
  sensor_width_mm: 17.3    # 传感器宽度
  sensor_height_mm: 13.0   # 传感器高度
  focal_length_mm: 24.0    # 焦距
  image_width_px: 5280     # 图像宽度
  image_height_px: 3956    # 图像高度
```

### SAHI参数优化

针对不同场景，可以调整SAHI切片参数：

```yaml
sahi:
  slice_height: 1280       # 切片高度（根据训练分辨率调整）
  slice_width: 1280        # 切片宽度
  overlap_height_ratio: 0.2 # 垂直重叠比例
  overlap_width_ratio: 0.2  # 水平重叠比例
```

### 检测阈值调整

```yaml
model:
  confidence_threshold: 0.25  # 置信度阈值（降低以检测更多目标）
  iou_threshold: 0.45         # NMS IoU阈值
```

## 🔧 故障排除

### 常见问题

1. **模型加载失败**
   - 检查 `best.pt` 文件是否存在
   - 确认模型版本与ultralytics兼容

2. **GPS信息提取失败**
   - 确保图片包含完整的EXIF GPS信息
   - 检查图片是否被处理软件修改过

3. **SAHI检测速度慢**
   - 减少切片尺寸或重叠比例
   - 使用GPU加速

4. **内存不足**
   - 减少批处理大小
   - 降低图片分辨率

### 依赖问题

如果遇到依赖安装问题：

```bash
# 使用国内镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple ultralytics sahi

# 或使用conda
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia
```

## 📈 性能优化

### GPU加速
```bash
# 检查CUDA是否可用
python -c "import torch; print(torch.cuda.is_available())"
```

### 批处理优化
```yaml
batch_processing:
  max_workers: 4    # 并行处理数
  chunk_size: 10    # 批次大小
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 📞 联系方式

- 项目维护者: 江豚保护项目组
- 邮箱: <EMAIL>
- 项目主页: https://github.com/your-repo/finless-porpoise-detection

## 🙏 致谢

- [Ultralytics](https://github.com/ultralytics/ultralytics) - YOLOv11实现
- [SAHI](https://github.com/obss/sahi) - 切片辅助超推理
- [OpenCV](https://opencv.org/) - 计算机视觉库
- DJI Mavic 3用户社区的测试和反馈

---

**⭐ 如果这个项目对您有帮助，请给个星标支持！** 