interface PorpoiseIllustrationProps {
  className?: string
  animated?: boolean
}

export default function PorpoiseIllustration({ className = "w-24 h-24", animated = false }: PorpoiseIllustrationProps) {
  return (
    <div className={`${className} ${animated ? 'porpoise-swim' : ''}`}>
      <svg viewBox="0 0 200 200" className="w-full h-full">
        {/* 江豚身体 */}
        <ellipse 
          cx="100" 
          cy="120" 
          rx="60" 
          ry="35" 
          fill="#64748b" 
          className="drop-shadow-lg"
        />
        
        {/* 江豚头部 */}
        <circle 
          cx="100" 
          cy="80" 
          r="35" 
          fill="#64748b" 
          className="drop-shadow-lg"
        />
        
        {/* 江豚眼睛 */}
        <circle cx="88" cy="70" r="8" fill="#1e293b" />
        <circle cx="112" cy="70" r="8" fill="#1e293b" />
        <circle cx="90" cy="68" r="3" fill="white" />
        <circle cx="114" cy="68" r="3" fill="white" />
        
        {/* 江豚嘴巴 */}
        <path 
          d="M 85 88 Q 100 95 115 88" 
          stroke="#1e293b" 
          strokeWidth="3" 
          fill="none"
          strokeLinecap="round"
        />
        
        {/* 江豚鳍 */}
        <ellipse 
          cx="70" 
          cy="110" 
          rx="12" 
          ry="20" 
          fill="#475569" 
          transform="rotate(-30 70 110)"
        />
        <ellipse 
          cx="130" 
          cy="110" 
          rx="12" 
          ry="20" 
          fill="#475569" 
          transform="rotate(30 130 110)"
        />
        
        {/* 江豚尾鳍 */}
        <path 
          d="M 160 120 Q 170 110 175 115 Q 170 125 160 120 Z" 
          fill="#475569"
        />
        <path 
          d="M 160 120 Q 170 130 175 125 Q 170 115 160 120 Z" 
          fill="#475569"
        />
        
        {/* 水花效果 */}
        {animated && (
          <>
            <circle cx="50" cy="160" r="4" fill="#38bdf8" opacity="0.6">
              <animate attributeName="r" values="4;8;4" dur="2s" repeatCount="indefinite" />
              <animate attributeName="opacity" values="0.6;0.2;0.6" dur="2s" repeatCount="indefinite" />
            </circle>
            <circle cx="150" cy="160" r="3" fill="#38bdf8" opacity="0.5">
              <animate attributeName="r" values="3;6;3" dur="1.8s" repeatCount="indefinite" />
              <animate attributeName="opacity" values="0.5;0.1;0.5" dur="1.8s" repeatCount="indefinite" />
            </circle>
            <circle cx="100" cy="170" r="5" fill="#0ea5e9" opacity="0.4">
              <animate attributeName="r" values="5;10;5" dur="2.5s" repeatCount="indefinite" />
              <animate attributeName="opacity" values="0.4;0.1;0.4" dur="2.5s" repeatCount="indefinite" />
            </circle>
          </>
        )}
        
        {/* 爱心效果（当检测到江豚时显示） */}
        {animated && (
          <g opacity="0.8">
            <path 
              d="M 180 50 C 180 45, 175 40, 170 40 C 165 40, 160 45, 160 50 C 160 45, 155 40, 150 40 C 145 40, 140 45, 140 50 C 140 60, 160 70, 160 70 C 160 70, 180 60, 180 50 Z" 
              fill="#f56b47"
            >
              <animateTransform 
                attributeName="transform" 
                type="scale" 
                values="0.8;1.2;0.8" 
                dur="2s" 
                repeatCount="indefinite"
              />
            </path>
          </g>
        )}
      </svg>
    </div>
  )
} 