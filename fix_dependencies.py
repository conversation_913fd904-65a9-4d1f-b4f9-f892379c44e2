#!/usr/bin/env python3
"""
江豚检测系统 - 依赖修复工具
自动检查和修复依赖问题
"""

import os
import sys
import subprocess
import importlib.util
import shutil
from pathlib import Path

def check_module(module_name):
    """检查模块是否已安装"""
    return importlib.util.find_spec(module_name) is not None

def install_package(package_name):
    """安装Python包"""
    print(f"正在安装 {package_name}...")
    
    # 处理多个包的情况
    if " " in package_name:
        packages = package_name.split()
        all_success = True
        for pkg in packages:
            success = install_single_package(pkg)
            if not success:
                all_success = False
        return all_success
    else:
        return install_single_package(package_name)

def install_single_package(package_name):
    """安装单个Python包"""
    result = subprocess.run(
        [sys.executable, "-m", "pip", "install", "-i", "https://pypi.tuna.tsinghua.edu.cn/simple", package_name],
        capture_output=True,
        text=True
    )
    
    if result.returncode == 0:
        print(f"✅ {package_name} 安装成功")
        return True
    else:
        print(f"❌ {package_name} 安装失败: {result.stderr}")
        return False

def check_and_install_dependencies():
    """检查并安装所有依赖"""
    dependencies = [
        ("torch", "torch torchvision torchaudio"),
        ("ultralytics", "ultralytics"),
        ("sahi", "sahi"),
        ("cv2", "opencv-python"),
        ("exifread", "exifread"),
        ("piexif", "piexif"),
        ("utm", "utm"),
        ("geopy", "geopy"),
        ("flask", "flask"),
        ("werkzeug", "werkzeug"),
        ("pandas", "pandas"),
        ("numpy", "numpy"),
        ("yaml", "pyyaml")
    ]
    
    all_success = True
    
    for module_name, package_name in dependencies:
        if not check_module(module_name):
            success = install_package(package_name)
            if not success:
                all_success = False
        else:
            try:
                # 使用importlib.metadata获取版本信息
                import importlib.metadata
                try:
                    # 首先尝试通过包名获取版本
                    package_map = {
                        "torch": "torch",
                        "ultralytics": "ultralytics", 
                        "sahi": "sahi",
                        "cv2": "opencv-python",
                        "exifread": "ExifRead",
                        "piexif": "piexif",
                        "utm": "utm",
                        "geopy": "geopy",
                        "flask": "flask",
                        "werkzeug": "werkzeug",
                        "pandas": "pandas",
                        "numpy": "numpy",
                        "yaml": "PyYAML"
                    }
                    
                    package_name = package_map.get(module_name, module_name)
                    version = importlib.metadata.version(package_name)
                    print(f"✅ {module_name} 已安装 ({version})")
                except importlib.metadata.PackageNotFoundError:
                    # 如果通过包名找不到，尝试导入模块获取版本
                    try:
                        module = importlib.import_module(module_name)
                        version = getattr(module, "__version__", "未知版本")
                        print(f"✅ {module_name} 已安装 ({version})")
                    except:
                        print(f"✅ {module_name} 已安装 (版本未知)")
            except ImportError:
                print(f"⚠️ {module_name} 已安装但无法导入")
            except Exception as e:
                print(f"✅ {module_name} 已安装 (版本检测失败: {e})")
    
    return all_success

def check_model_file():
    """检查模型文件是否存在"""
    model_path = Path("best.pt")
    if model_path.exists():
        print(f"✅ 模型文件已存在: {model_path}")
        return True
    else:
        print(f"❌ 模型文件不存在: {model_path}")
        return False

def check_config_file():
    """检查配置文件是否存在，如不存在则创建默认配置"""
    config_path = Path("config.yaml")
    if config_path.exists():
        print(f"✅ 配置文件已存在: {config_path}")
        return True
    else:
        print(f"❌ 配置文件不存在: {config_path}")
        print("创建默认配置文件...")
        
        default_config = """# 江豚检测系统配置文件
model:
  weights_path: best.pt
  confidence_threshold: 0.25
  iou_threshold: 0.45
  device: auto  # auto, cpu, cuda:0

sahi:
  enabled: true
  slice_height: 1280
  slice_width: 1280
  overlap_height_ratio: 0.2
  overlap_width_ratio: 0.2

mavic3_camera:
  sensor_width_mm: 17.3
  sensor_height_mm: 13.0
  focal_length_mm: 24.0
  image_width_px: 5280
  image_height_px: 3956

gps:
  altitude_offset: 0.0
  coordinate_system: WGS84
  precision_decimal: 6

logging:
  save_to_file: true
  log_level: INFO
"""
        
        try:
            with open(config_path, "w", encoding="utf-8") as f:
                f.write(default_config)
            print(f"✅ 默认配置文件已创建: {config_path}")
            return True
        except Exception as e:
            print(f"❌ 创建配置文件失败: {e}")
            return False

def check_directory_structure():
    """检查并创建必要的目录结构"""
    directories = [
        "uploads",
        "web_results",
        "static",
        "templates"
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            try:
                dir_path.mkdir(exist_ok=True)
                print(f"✅ 创建目录: {dir_path}")
            except Exception as e:
                print(f"❌ 创建目录失败 {dir_path}: {e}")

def fix_module_imports():
    """尝试修复模块导入问题"""
    # 检查是否有sys.path问题
    src_path = Path("src")
    if src_path.exists() and str(src_path.absolute()) not in sys.path:
        sys.path.insert(0, str(src_path.absolute()))
        print(f"✅ 已添加 {src_path.absolute()} 到Python路径")
    
    # 检查detector模块
    try:
        from src.detector import FinlessPorpoiseDetector
        print("✅ 成功导入检测器模块")
    except ImportError as e:
        print(f"❌ 导入检测器模块失败: {e}")

def check_pytorch_cuda():
    """检查PyTorch CUDA支持"""
    try:
        import torch
        cuda_available = torch.cuda.is_available()
        if cuda_available:
            device_count = torch.cuda.device_count()
            device_name = torch.cuda.get_device_name(0) if device_count > 0 else "未知"
            print(f"✅ PyTorch CUDA可用: {device_count}个设备")
            print(f"   设备名称: {device_name}")
        else:
            print("⚠️ PyTorch CUDA不可用，将使用CPU模式")
    except ImportError:
        print("❌ 无法导入PyTorch")

def main():
    """主函数"""
    print("\n" + "="*50)
    print("🐋 江豚检测系统 - 依赖修复工具")
    print("="*50)
    
    print("\n📋 检查Python环境...")
    print(f"Python版本: {sys.version}")
    
    print("\n📦 检查并安装依赖...")
    deps_ok = check_and_install_dependencies()
    
    print("\n🔍 检查目录结构...")
    check_directory_structure()
    
    print("\n📄 检查配置文件...")
    config_ok = check_config_file()
    
    print("\n🧠 检查模型文件...")
    model_ok = check_model_file()
    
    print("\n🔧 修复模块导入问题...")
    fix_module_imports()
    
    print("\n🖥️ 检查CUDA支持...")
    check_pytorch_cuda()
    
    print("\n" + "="*50)
    if deps_ok and config_ok and model_ok:
        print("✅ 依赖修复完成！系统应该可以正常运行了")
        print("🚀 请运行 'python web_app.py' 启动系统")
    else:
        print("⚠️ 部分问题未解决，请查看上面的错误信息")
    print("="*50)

if __name__ == "__main__":
    main() 