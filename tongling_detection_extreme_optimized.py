#!/usr/bin/env python3
"""
铜陵无人机数据检测脚本 - 极致优化版本
使用正确的autobatch参数，最大化GPU和CPU利用率
"""

import os
import sys
import cv2
import json
import csv
import logging
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Tuple
import exifread
import re
import torch
from ultralytics import YOLO
from ultralytics.utils.autobatch import autobatch
from collections import defaultdict
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings
import matplotlib.pyplot as plt
import seaborn as sns
import folium
import tempfile
import uuid
from tqdm import tqdm
import multiprocessing as mp
from functools import partial
import gc
import psutil
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class TonglingExtremeOptimizedDetector:
    def __init__(self, model_path="best.engine", confidence_threshold=0.5, 
                 slice_size=1280, max_workers=None, max_memory_gb=200):
        """
        初始化极致优化检测器
        
        Args:
            model_path: TensorRT模型路径
            confidence_threshold: 置信度阈值
            slice_size: SAHI切片尺寸
            max_workers: 最大工作线程数
            max_memory_gb: 最大内存使用量(GB)
        """
        self.model_path = model_path
        self.confidence_threshold = confidence_threshold
        self.slice_size = slice_size
        self.max_workers = max_workers or min(64, mp.cpu_count())  # 最大化线程数
        self.max_memory_gb = max_memory_gb
        self.results_dir = Path("tongling_results_extreme_optimized")
        self.results_dir.mkdir(exist_ok=True)
        
        # 创建临时目录
        self.temp_dir = Path(tempfile.mkdtemp(prefix="tongling_"))
        
        # 初始化日志
        self.setup_logging()
        
        # 加载TensorRT模型
        self.load_model()
        
        # 检测结果存储
        self.all_detections = []
        self.tracking_results = {}
        
        # 进度统计
        self.total_files = 0
        self.processed_files = 0
        self.lock = threading.Lock()
        
        # 内存监控
        self.memory_monitor = MemoryMonitor(max_memory_gb)
        
    def setup_logging(self):
        """设置日志"""
        log_file = self.results_dir / f"detection_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('TonglingExtremeOptimizedDetector')
        
    def load_model(self):
        """加载TensorRT模型并极致优化配置"""
        try:
            self.logger.info(f"正在加载TensorRT模型: {self.model_path}")
            
            # 使用ultralytics加载TensorRT模型
            self.model = YOLO(self.model_path, task="detect")
            
            # 设置设备
            if torch.cuda.is_available():
                self.device = "cuda"
                self.logger.info(f"使用GPU: {torch.cuda.get_device_name(0)}")
                
                # 设置CUDA优化
                torch.backends.cudnn.benchmark = True
                torch.backends.cudnn.deterministic = False
                
                # 清理GPU缓存
                torch.cuda.empty_cache()
                
                # 设置GPU内存分配策略 - 使用更多显存
                torch.cuda.set_per_process_memory_fraction(0.98)  # 使用98%显存
                
                # 使用autobatch自动确定最优批处理大小 - 使用正确的参数
                self.batch_size = self._determine_optimal_batch_size()
                self.logger.info(f"自动确定的最优批处理大小: {self.batch_size}")
                
                # 预热GPU
                self._warmup_gpu()
            else:
                self.device = "cpu"
                self.batch_size = 32  # CPU批处理大小
                self.logger.info("使用CPU")
                
            self.logger.info("TensorRT模型加载成功")
        except Exception as e:
            self.logger.error(f"TensorRT模型加载失败: {e}")
            raise
            
    def _determine_optimal_batch_size(self):
        """使用autobatch确定最优批处理大小 - 使用正确的参数"""
        try:
            self.logger.info("正在确定最优批处理大小...")
            
            # 创建测试图像
            test_img = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
            test_path = self.temp_dir / "autobatch_test.jpg"
            cv2.imwrite(str(test_path), test_img)
            
            # 使用autobatch确定最优批处理大小 - 使用正确的参数
            optimal_batch = autobatch(self.model, device=self.device)
            
            # 清理测试文件
            test_path.unlink(missing_ok=True)
            
            # 确保批处理大小合理，但更大
            optimal_batch = max(1, min(optimal_batch, 128))  # 增加到128
            
            return optimal_batch
            
        except Exception as e:
            self.logger.warning(f"自动批处理大小确定失败: {e}，使用默认值64")
            return 64
            
    def _warmup_gpu(self):
        """预热GPU"""
        try:
            self.logger.info("预热GPU...")
            # 创建一个小的测试图像
            dummy_img = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
            dummy_path = self.temp_dir / "warmup.jpg"
            cv2.imwrite(str(dummy_path), dummy_img)
            
            # 运行几次推理来预热GPU
            for _ in range(10):  # 增加预热次数
                results = self.model(
                    str(dummy_path), 
                    conf=self.confidence_threshold, 
                    device=self.device, 
                    verbose=False,
                    stream=False  # 关闭流式处理
                )
                
            # 清理测试文件
            dummy_path.unlink(missing_ok=True)
            torch.cuda.empty_cache()
            self.logger.info("GPU预热完成")
        except Exception as e:
            self.logger.warning(f"GPU预热失败: {e}")
            
    def extract_gps_from_exif(self, image_path: str) -> Dict[str, float]:
        """从图片EXIF信息中提取GPS坐标"""
        try:
            with open(image_path, 'rb') as f:
                tags = exifread.process_file(f)
            
            gps_info = {}
            
            # 提取GPS坐标
            if 'GPS GPSLatitude' in tags and 'GPS GPSLongitude' in tags:
                lat = self._convert_to_degrees(tags['GPS GPSLatitude'].values)
                lon = self._convert_to_degrees(tags['GPS GPSLongitude'].values)
                
                # 处理南北半球
                if 'GPS GPSLatitudeRef' in tags:
                    if tags['GPS GPSLatitudeRef'].values == 'S':
                        lat = -lat
                        
                # 处理东西半球
                if 'GPS GPSLongitudeRef' in tags:
                    if tags['GPS GPSLongitudeRef'].values == 'W':
                        lon = -lon
                        
                gps_info['latitude'] = lat
                gps_info['longitude'] = lon
                
            # 提取高度信息
            if 'GPS GPSAltitude' in tags:
                altitude = float(tags['GPS GPSAltitude'].values[0])
                gps_info['altitude'] = altitude
                
            # 提取时间信息
            if 'EXIF DateTimeOriginal' in tags:
                gps_info['timestamp'] = str(tags['EXIF DateTimeOriginal'].values)
                
            return gps_info
            
        except Exception as e:
            self.logger.warning(f"提取GPS信息失败 {image_path}: {e}")
            return {}
            
    def _convert_to_degrees(self, values):
        """将GPS坐标转换为度"""
        d = float(values[0].num) / float(values[0].den)
        m = float(values[1].num) / float(values[1].den)
        s = float(values[2].num) / float(values[2].den)
        return d + (m / 60.0) + (s / 3600.0)
        
    def extract_gps_from_srt(self, srt_path: str) -> Dict[str, List[Dict]]:
        """从SRT字幕文件中提取GPS信息"""
        gps_data = []
        try:
            with open(srt_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 解析SRT文件
            blocks = content.strip().split('\n\n')
            for block in blocks:
                lines = block.strip().split('\n')
                if len(lines) >= 3:
                    # 提取时间戳
                    time_line = lines[1]
                    time_match = re.search(r'(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})', time_line)
                    
                    if time_match:
                        start_time = time_match.group(1)
                        end_time = time_match.group(2)
                        
                        # 提取GPS信息
                        gps_info = {}
                        for line in lines[2:]:
                            if 'GPS' in line or 'latitude' in line.lower() or 'longitude' in line.lower():
                                # 提取经纬度
                                lat_match = re.search(r'lat[itude]*[:\s]*([-\d.]+)', line, re.IGNORECASE)
                                lon_match = re.search(r'lon[gitude]*[:\s]*([-\d.]+)', line, re.IGNORECASE)
                                alt_match = re.search(r'alt[itude]*[:\s]*([-\d.]+)', line, re.IGNORECASE)
                                
                                if lat_match:
                                    gps_info['latitude'] = float(lat_match.group(1))
                                if lon_match:
                                    gps_info['longitude'] = float(lon_match.group(1))
                                if alt_match:
                                    gps_info['altitude'] = float(alt_match.group(1))
                                    
                        if gps_info:
                            gps_info['start_time'] = start_time
                            gps_info['end_time'] = end_time
                            gps_data.append(gps_info)
                            
        except Exception as e:
            self.logger.warning(f"提取SRT GPS信息失败 {srt_path}: {e}")
            
        return gps_data
        
    def create_slices_extreme_optimized(self, image: np.ndarray, slice_size: int = 1280) -> List[Tuple[np.ndarray, Tuple[int, int]]]:
        """创建极致优化的SAHI切片"""
        slices = []
        h, w = image.shape[:2]
        
        # 如果图像小于切片尺寸，直接返回
        if h <= slice_size and w <= slice_size:
            return [(image, (0, 0))]
        
        # 计算切片数量，优化GPU利用率
        stride = slice_size * 4 // 5  # 20%重叠，减少切片数量
        for y in range(0, h, stride):
            for x in range(0, w, stride):
                # 提取切片
                y_end = min(y + slice_size, h)
                x_end = min(x + slice_size, w)
                slice_img = image[y:y_end, x:x_end]
                
                # 如果切片太小，跳过
                if slice_img.shape[0] < slice_size // 2 or slice_img.shape[1] < slice_size // 2:
                    continue
                    
                slices.append((slice_img, (x, y)))
                
        return slices
        
    def detect_image_batch_extreme_optimized(self, image_paths: List[str], gps_infos: List[Dict] = None) -> List[List[Dict]]:
        """极致优化的批量检测图片"""
        try:
            # 检查内存使用
            if not self.memory_monitor.check_memory():
                self.logger.warning("内存使用过高，清理缓存")
                self.cleanup_memory()
            
            # 使用TensorRT模型进行批量检测，最大化GPU利用率
            results = self.model(
                image_paths, 
                conf=self.confidence_threshold, 
                device=self.device, 
                verbose=False,
                stream=False,  # 关闭流式处理以提高GPU利用率
                half=True,     # 使用FP16加速
                agnostic_nms=True,  # 类别无关NMS
                max_det=2000   # 增加最大检测数量
            )
            
            all_detections = []
            
            for i, result in enumerate(results):
                detections = []
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())
                        
                        detection_info = {
                            'bbox_x1': float(x1),
                            'bbox_y1': float(y1),
                            'bbox_x2': float(x2),
                            'bbox_y2': float(y2),
                            'confidence': float(confidence),
                            'class_id': class_id,
                            'class_name': 'finless_porpoise',
                            'detection_method': 'TensorRT_Extreme_Optimized',
                            'image_path': image_paths[i] if i < len(image_paths) else "unknown"
                        }
                        
                        # 添加GPS信息
                        if gps_infos and i < len(gps_infos) and gps_infos[i]:
                            detection_info.update(gps_infos[i])
                            
                        detections.append(detection_info)
                        
                all_detections.append(detections)
                
            return all_detections
            
        except Exception as e:
            self.logger.error(f"极致优化批量图片检测失败: {e}")
            return [[] for _ in image_paths]
            
    def detect_image_with_sahi_extreme_optimized(self, image_path: str, gps_info: Dict = None) -> List[Dict]:
        """使用极致优化的SAHI切片检测图片"""
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                self.logger.error(f"无法读取图像: {image_path}")
                return []
                
            h, w = image.shape[:2]
            
            # 如果图像小于切片尺寸，直接检测
            if h <= self.slice_size and w <= self.slice_size:
                detections = self.detect_image_batch_extreme_optimized([image_path], [gps_info])
                return detections[0] if detections else []
            
            # 创建切片
            slices = self.create_slices_extreme_optimized(image, self.slice_size)
            self.logger.info(f"图像 {image_path} 创建了 {len(slices)} 个切片")
            
            all_detections = []
            
            # 批量处理切片
            slice_paths = []
            slice_gps_infos = []
            
            for i, (slice_img, (offset_x, offset_y)) in enumerate(slices):
                # 保存临时切片
                temp_slice_path = self.temp_dir / f"slice_{uuid.uuid4().hex}.jpg"
                cv2.imwrite(str(temp_slice_path), slice_img)
                slice_paths.append(str(temp_slice_path))
                slice_gps_infos.append(gps_info)
                
                # 每处理一定数量的切片就进行批量检测
                if len(slice_paths) >= self.batch_size or i == len(slices) - 1:
                    # 批量检测
                    batch_detections = self.detect_image_batch_extreme_optimized(slice_paths, slice_gps_infos)
                    
                    # 调整坐标到原图
                    for j, detections in enumerate(batch_detections):
                        if j < len(slices):
                            offset_x, offset_y = slices[i - len(slice_paths) + j + 1][1]
                            for detection in detections:
                                detection['bbox_x1'] += offset_x
                                detection['bbox_y1'] += offset_y
                                detection['bbox_x2'] += offset_x
                                detection['bbox_y2'] += offset_y
                                detection['slice_offset'] = (offset_x, offset_y)
                                detection['slice_index'] = i - len(slice_paths) + j + 1
                                
                            all_detections.extend(detections)
                    
                    # 清理临时文件
                    for slice_path in slice_paths:
                        Path(slice_path).unlink(missing_ok=True)
                    
                    # 重置批次
                    slice_paths = []
                    slice_gps_infos = []
                    
                    # 清理内存
                    self.cleanup_memory()
            
            # 非极大值抑制去重
            if all_detections:
                all_detections = self.non_max_suppression(all_detections)
                
            return all_detections
            
        except Exception as e:
            self.logger.error(f"极致优化SAHI图片检测失败 {image_path}: {e}")
            return []
            
    def non_max_suppression(self, detections: List[Dict], iou_threshold: float = 0.5) -> List[Dict]:
        """非极大值抑制去重"""
        if not detections:
            return []
            
        # 按置信度排序
        detections = sorted(detections, key=lambda x: x['confidence'], reverse=True)
        
        kept_detections = []
        
        for detection in detections:
            should_keep = True
            
            for kept in kept_detections:
                # 计算IoU
                iou = self.calculate_iou(detection, kept)
                if iou > iou_threshold:
                    should_keep = False
                    break
                    
            if should_keep:
                kept_detections.append(detection)
                
        return kept_detections
        
    def calculate_iou(self, box1: Dict, box2: Dict) -> float:
        """计算两个检测框的IoU"""
        x1_1, y1_1, x2_1, y2_1 = box1['bbox_x1'], box1['bbox_y1'], box1['bbox_x2'], box1['bbox_y2']
        x1_2, y1_2, x2_2, y2_2 = box2['bbox_x1'], box2['bbox_y1'], box2['bbox_x2'], box2['bbox_y2']
        
        # 计算交集
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
            
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        
        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
        
    def detect_video_extreme_optimized(self, video_path: str, srt_path: str = None) -> List[Dict]:
        """极致优化的视频检测"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                self.logger.error(f"无法打开视频: {video_path}")
                return []
                
            # 获取视频信息
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps
            
            self.logger.info(f"视频信息: {video_path}")
            self.logger.info(f"FPS: {fps}, 总帧数: {total_frames}, 时长: {duration:.2f}秒")
            
            # 提取GPS信息
            gps_data = []
            if srt_path and os.path.exists(srt_path):
                gps_data = self.extract_gps_from_srt(srt_path)
                self.logger.info(f"从SRT文件提取到 {len(gps_data)} 条GPS记录")
            
            # 每3秒提取一帧进行检测
            frame_interval = int(fps * 3)
            detections = []
            frame_count = 0
            
            # 批量处理帧
            frame_batch = []
            frame_times = []
            frame_numbers = []
            
            with tqdm(total=total_frames // frame_interval, desc=f"处理视频: {Path(video_path).name}") as pbar:
                while True:
                    ret, frame = cap.read()
                    if not ret:
                        break
                        
                    # 每3秒处理一帧
                    if frame_count % frame_interval == 0:
                        # 保存临时帧到临时目录
                        temp_frame_path = self.temp_dir / f"frame_{frame_count}_{uuid.uuid4().hex}.jpg"
                        cv2.imwrite(str(temp_frame_path), frame)
                        
                        # 获取当前时间对应的GPS信息
                        current_time = frame_count / fps
                        current_gps = self._get_gps_at_time(gps_data, current_time)
                        
                        frame_batch.append(str(temp_frame_path))
                        frame_times.append(current_time)
                        frame_numbers.append(frame_count)
                        
                        # 当批次满了或到达最后一帧时进行检测
                        if len(frame_batch) >= self.batch_size or frame_count + frame_interval >= total_frames:
                            # 批量检测
                            batch_detections = self.detect_image_batch_extreme_optimized(frame_batch, [{}] * len(frame_batch))
                            
                            # 添加时间信息
                            for i, frame_detections in enumerate(batch_detections):
                                for detection in frame_detections:
                                    detection['frame_time'] = frame_times[i]
                                    detection['frame_number'] = frame_numbers[i]
                                    
                                detections.extend(frame_detections)
                            
                            # 清理临时文件
                            for frame_path in frame_batch:
                                Path(frame_path).unlink(missing_ok=True)
                            
                            # 重置批次
                            frame_batch = []
                            frame_times = []
                            frame_numbers = []
                            
                            # 清理内存
                            self.cleanup_memory()
                            
                            pbar.update(len(batch_detections))
                        
                    frame_count += 1
                    
            cap.release()
            return detections
            
        except Exception as e:
            self.logger.error(f"极致优化视频检测失败 {video_path}: {e}")
            return []
            
    def _get_gps_at_time(self, gps_data: List[Dict], current_time: float) -> Dict:
        """根据时间获取对应的GPS信息"""
        if not gps_data:
            return {}
            
        # 找到最接近当前时间的GPS记录
        for gps_record in gps_data:
            start_time = self._parse_srt_time(gps_record.get('start_time', '00:00:00,000'))
            end_time = self._parse_srt_time(gps_record.get('end_time', '00:00:00,000'))
            
            if start_time <= current_time <= end_time:
                return gps_record
                
        return {}
        
    def _parse_srt_time(self, time_str: str) -> float:
        """解析SRT时间格式"""
        try:
            time_parts = time_str.replace(',', '.').split(':')
            hours = int(time_parts[0])
            minutes = int(time_parts[1])
            seconds = float(time_parts[2])
            return hours * 3600 + minutes * 60 + seconds
        except:
            return 0.0
            
    def cleanup_memory(self):
        """清理内存"""
        # 清理Python垃圾回收
        gc.collect()
        
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            
        # 检查内存使用
        self.memory_monitor.log_memory_usage()
        
    def process_single_file(self, file_info: Tuple[str, str, Dict]) -> List[Dict]:
        """处理单个文件"""
        file_path, file_type, gps_info = file_info
        
        try:
            if file_type == 'image':
                detections = self.detect_image_with_sahi_extreme_optimized(file_path, gps_info)
            else:  # video
                # 查找对应的SRT文件
                video_path = Path(file_path)
                srt_file = video_path.with_suffix('.srt')
                if not srt_file.exists():
                    srt_file = video_path.with_suffix('.SRT')
                    
                detections = self.detect_video_extreme_optimized(file_path, str(srt_file) if srt_file.exists() else None)
                
            # 更新进度
            with self.lock:
                self.processed_files += 1
                
            return detections
            
        except Exception as e:
            self.logger.error(f"处理文件失败 {file_path}: {e}")
            return []
            
    def process_folder(self, folder_path: str) -> List[Dict]:
        """处理整个文件夹"""
        folder_path = Path(folder_path)
        self.logger.info(f"开始处理文件夹: {folder_path}")
        
        # 创建结果子目录
        folder_results_dir = self.results_dir / folder_path.name
        folder_results_dir.mkdir(exist_ok=True)
        
        # 收集所有文件
        image_files = []
        video_files = []
        
        for file_path in folder_path.rglob("*"):
            if file_path.is_file():
                ext = file_path.suffix.lower()
                if ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                    image_files.append(file_path)
                elif ext in ['.mp4', '.avi', '.mov', '.mkv']:
                    video_files.append(file_path)
                    
        self.logger.info(f"找到 {len(image_files)} 个图片文件和 {len(video_files)} 个视频文件")
        
        # 准备文件列表
        files_to_process = []
        
        # 添加图片文件
        for image_file in image_files:
            gps_info = self.extract_gps_from_exif(str(image_file))
            files_to_process.append((str(image_file), 'image', gps_info))
            
        # 添加视频文件
        for video_file in video_files:
            files_to_process.append((str(video_file), 'video', {}))
            
        # 使用多线程处理
        all_detections = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_file = {executor.submit(self.process_single_file, file_info): file_info[0] 
                            for file_info in files_to_process}
            
            # 收集结果
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    detections = future.result()
                    all_detections.extend(detections)
                    self.logger.info(f"完成处理: {Path(file_path).name}")
                except Exception as e:
                    self.logger.error(f"处理失败 {file_path}: {e}")
                    
        # 保存结果
        self._save_results(all_detections, folder_results_dir, folder_path.name)
        
        return all_detections
        
    def _save_results(self, detections: List[Dict], results_dir: Path, folder_name: str):
        """保存检测结果"""
        # 保存CSV文件
        csv_file = results_dir / f"{folder_name}_detections.csv"
        if detections:
            df = pd.DataFrame(detections)
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            self.logger.info(f"检测结果已保存到: {csv_file}")
            
        # 保存JSON文件
        json_file = results_dir / f"{folder_name}_detections.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(detections, f, ensure_ascii=False, indent=2)
            
        # 生成统计报告
        self._generate_report(detections, results_dir, folder_name)
        
    def _generate_report(self, detections: List[Dict], results_dir: Path, folder_name: str):
        """生成检测报告"""
        if not detections:
            return
            
        # 统计信息
        total_detections = len(detections)
        confidence_scores = [d['confidence'] for d in detections]
        avg_confidence = np.mean(confidence_scores)
        max_confidence = np.max(confidence_scores)
        min_confidence = np.min(confidence_scores)
        
        # 生成报告
        report = f"""
# 铜陵无人机检测报告 - {folder_name} (极致优化版本)

## 检测统计
- 总检测数量: {total_detections}
- 平均置信度: {avg_confidence:.3f}
- 最高置信度: {max_confidence:.3f}
- 最低置信度: {min_confidence:.3f}

## 检测方法
- 极致优化TensorRT推理引擎
- 自动批处理大小优化 (大小: {self.batch_size})
- 批量SAHI切片检测 (尺寸: {self.slice_size})
- 多线程并行处理 ({self.max_workers} 线程)
- FP16加速推理
- 非极大值抑制去重

## 性能信息
- 模型: best.engine (TensorRT)
- 推理框架: ultralytics + TensorRT
- 硬件加速: {self.device.upper()}
- 批处理大小: {self.batch_size} (自动优化)
- 最大内存使用: {self.max_memory_gb}GB
"""
        
        # 保存报告
        report_file = results_dir / f"{folder_name}_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
            
        # 生成可视化图表
        self._generate_visualizations(detections, results_dir, folder_name)
        
    def _generate_visualizations(self, detections: List[Dict], results_dir: Path, folder_name: str):
        """生成可视化图表"""
        if not detections:
            return
            
        # 置信度分布图
        plt.figure(figsize=(10, 6))
        confidence_scores = [d['confidence'] for d in detections]
        plt.hist(confidence_scores, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        plt.xlabel('置信度')
        plt.ylabel('检测数量')
        plt.title(f'{folder_name} - 极致优化检测置信度分布')
        plt.grid(True, alpha=0.3)
        plt.savefig(results_dir / f"{folder_name}_confidence_distribution.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        # GPS分布图（如果有GPS信息）
        gps_detections = [d for d in detections if 'latitude' in d and 'longitude' in d]
        if gps_detections:
            plt.figure(figsize=(12, 8))
            lats = [d['latitude'] for d in gps_detections]
            lons = [d['longitude'] for d in gps_detections]
            confidences = [d['confidence'] for d in gps_detections]
            
            scatter = plt.scatter(lons, lats, c=confidences, cmap='viridis', s=50, alpha=0.7)
            plt.colorbar(scatter, label='置信度')
            plt.xlabel('经度')
            plt.ylabel('纬度')
            plt.title(f'{folder_name} - 极致优化检测目标GPS分布')
            plt.grid(True, alpha=0.3)
            plt.savefig(results_dir / f"{folder_name}_gps_distribution.png", dpi=300, bbox_inches='tight')
            plt.close()
            
    def process_all_folders(self, base_path: str):
        """处理所有子文件夹"""
        base_path = Path(base_path)
        self.logger.info(f"开始处理所有文件夹: {base_path}")
        
        # 获取所有子文件夹
        subfolders = [f for f in base_path.iterdir() if f.is_dir()]
        self.logger.info(f"找到 {len(subfolders)} 个子文件夹")
        
        all_results = {}
        
        # 使用多线程处理文件夹
        with ThreadPoolExecutor(max_workers=min(16, len(subfolders))) as executor:  # 增加文件夹并行数
            future_to_folder = {executor.submit(self.process_folder, str(folder)): folder for folder in subfolders}
            
            for future in as_completed(future_to_folder):
                folder = future_to_folder[future]
                try:
                    detections = future.result()
                    all_results[folder.name] = detections
                    self.logger.info(f"完成处理: {folder.name}")
                except Exception as e:
                    self.logger.error(f"处理失败 {folder.name}: {e}")
                    
        # 生成总体报告
        self._generate_summary_report(all_results)
        
        # 清理临时目录
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
    def _generate_summary_report(self, all_results: Dict[str, List[Dict]]):
        """生成总体报告"""
        summary_file = self.results_dir / "总体检测报告_极致优化版本.md"
        
        total_detections = sum(len(detections) for detections in all_results.values())
        total_folders = len(all_results)
        
        summary = f"""
# 铜陵无人机检测总体报告 - 极致优化版本

## 检测统计
- 总文件夹数: {total_folders}
- 总检测数量: {total_detections}
- 推理引擎: 极致优化TensorRT + 自动批处理优化
- 模型文件: best.engine
- 设备: {self.device.upper()}
- 多线程数: {self.max_workers}
- SAHI切片尺寸: {self.slice_size}
- 批处理大小: {self.batch_size} (自动优化)
- 最大内存使用: {self.max_memory_gb}GB

## 各文件夹检测结果
"""
        
        for folder_name, detections in all_results.items():
            if detections:
                avg_confidence = np.mean([d['confidence'] for d in detections])
                summary += f"- {folder_name}: {len(detections)} 个检测，平均置信度 {avg_confidence:.3f}\n"
            else:
                summary += f"- {folder_name}: 无检测结果\n"
                
        # 保存总体报告
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary)
            
        self.logger.info(f"总体报告已保存到: {summary_file}")

class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, max_memory_gb: float):
        self.max_memory_gb = max_memory_gb
        self.max_memory_bytes = max_memory_gb * 1024**3
        
    def check_memory(self) -> bool:
        """检查内存使用"""
        memory = psutil.virtual_memory()
        return memory.used < self.max_memory_bytes
        
    def log_memory_usage(self):
        """记录内存使用"""
        memory = psutil.virtual_memory()
        used_gb = memory.used / 1024**3
        total_gb = memory.total / 1024**3
        logging.info(f"内存使用: {used_gb:.1f}GB / {total_gb:.1f}GB ({memory.percent}%)")

def main():
    """主函数"""
    print("🐋 铜陵无人机数据检测系统 - 极致优化版本")
    print("="*50)
    
    # 检查模型文件
    model_path = "best.engine"
    if not os.path.exists(model_path):
        print(f"❌ TensorRT模型文件不存在: {model_path}")
        return
        
    # 检查数据文件夹
    data_path = "20250718铜陵"
    if not os.path.exists(data_path):
        print(f"❌ 数据文件夹不存在: {data_path}")
        return
        
    # 初始化检测器
    detector = TonglingExtremeOptimizedDetector(
        model_path=model_path,
        confidence_threshold=0.5,
        slice_size=1280,
        max_workers=64,  # 最大化线程数
        max_memory_gb=200  # 限制内存使用
    )
    
    # 处理所有文件夹
    detector.process_all_folders(data_path)
    
    print("🎉 极致优化检测完成！结果保存在 tongling_results_extreme_optimized 文件夹中")

if __name__ == "__main__":
    main() 