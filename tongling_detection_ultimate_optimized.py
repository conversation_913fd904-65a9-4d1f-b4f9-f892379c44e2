# ===================================================================================
# Final Detector Script v5.2 - SAHI for Videos
#
# Features:
# - SAHI slicing is now enabled for video frames for higher detection accuracy.
# - Reports for videos are now based on total detections per frame.
# - All other features (image processing, output structure) are retained.
# ===================================================================================

print("INFO: Script starting. Initializing imports...")

try:
    import os
    import cv2
    import torch
    import pandas as pd
    import time
    import piexif
    import shutil
    from ultralytics import YOLO
    from sahi import AutoDetectionModel
    from sahi.predict import get_sliced_prediction
    from math import tan, atan, pi, cos
    from multiprocessing import Pool, cpu_count
    from collections import defaultdict
    from tqdm import tqdm
    print("INFO: All libraries imported successfully.")
except ImportError as e:
    print(f"\nFATAL: A critical library failed to import: {e}"); exit()

# --- 1. 配置区域 ---
MODEL_PATH = r"D:\anfh_workspace\jiangtun\best.engine"
BASE_DATA_DIR = r"20250718铜陵"
BASE_OUTPUT_DIR = "outputs"
REPORTS_DIR = os.path.join(BASE_OUTPUT_DIR, "reports")
ORIGINAL_IMG_DIR = os.path.join(BASE_OUTPUT_DIR, "images_with_detections", "original")
ANNOTATED_IMG_DIR = os.path.join(BASE_OUTPUT_DIR, "images_with_detections", "annotated")
ORIGINAL_KF_DIR = os.path.join(BASE_OUTPUT_DIR, "video_keyframes", "original")
ANNOTATED_KF_DIR = os.path.join(BASE_OUTPUT_DIR, "video_keyframes", "annotated")

# --- 检测与处理配置 ---
# ======================== 新增配置 ========================
USE_SAHI_FOR_VIDEOS = True # <--- 总开关：在视频上启用SAHI
# ==========================================================
VIDEO_FPS_TO_PROCESS = 2   # <--- 已根据您的要求修改为2 FPS
# ==========================================================
USE_SAHI_FOR_IMAGES = True; SLICE_WIDTH = 1280; SLICE_HEIGHT = 1280; OVERLAP_RATIO = 0.2
TRACKER_CONFIG = 'bytetrack.yaml' # (仅在 USE_SAHI_FOR_VIDEOS = False 时使用)
CAMERA_FOCAL_LENGTH_MM = 28; CAMERA_SENSOR_WIDTH_MM = 12.8; CAMERA_SENSOR_HEIGHT_MM = 9.6

# --- 2. 辅助功能函数 (保持不变) ---
def dms_to_decimal(dms, ref):
    degrees = dms[0][0] / dms[0][1]; minutes = dms[1][0] / dms[1][1]; seconds = dms[2][0] / dms[2][1]
    decimal = degrees + (minutes / 60.0) + (seconds / 3600.0)
    if ref in ['S', 'W']: decimal = -decimal
    return decimal
def get_image_geodata(image_path):
    try:
        exif_dict = piexif.load(image_path); gps_info = exif_dict.get('GPS')
        if not gps_info: return None
        lat = dms_to_decimal(gps_info[piexif.GPSIFD.GPSLatitude], gps_info[piexif.GPSIFD.GPSLatitudeRef].decode())
        lon = dms_to_decimal(gps_info[piexif.GPSIFD.GPSLongitude], gps_info[piexif.GPSIFD.GPSLongitudeRef].decode())
        alt = gps_info[piexif.GPSIFD.GPSAltitude][0] / gps_info[piexif.GPSIFD.GPSAltitude][1]
        return {'lat': lat, 'lon': lon, 'alt': alt}
    except Exception: return None
def calculate_target_gps(drone_geo, drone_alt_agl, img_width, img_height, target_bbox):
    if not drone_geo or drone_alt_agl is None or drone_alt_agl <= 0: return None
    fov_h = 2 * atan((CAMERA_SENSOR_WIDTH_MM / 2) / CAMERA_FOCAL_LENGTH_MM); gsd_h = (2 * drone_alt_agl * tan(fov_h / 2)) / img_width
    target_x_px = (target_bbox[0] + target_bbox[2]) / 2; center_x_px = img_width / 2; delta_x_meters = (target_x_px - center_x_px) * gsd_h
    fov_v = 2 * atan((CAMERA_SENSOR_HEIGHT_MM / 2) / CAMERA_FOCAL_LENGTH_MM); gsd_v = (2 * drone_alt_agl * tan(fov_v / 2)) / img_height
    target_y_px = (target_bbox[1] + target_bbox[3]) / 2; center_y_px = img_height / 2; delta_y_meters = (center_y_px - target_y_px) * gsd_v
    R = 6378137.0; delta_lat = (delta_y_meters / R) * (180 / pi); delta_lon = (delta_x_meters / (R * cos(pi * drone_geo['lat'] / 180))) * (180 / pi)
    return {'lat': drone_geo['lat'] + delta_lat, 'lon': drone_geo['lon'] + delta_lon}
def draw_detections(image, detections):
    for det in detections:
        box = det['bbox_xyxy']; label = f"{det['class_name']}: {det['confidence']}"
        cv2.rectangle(image, (box[0], box[1]), (box[2], box[3]), (0, 255, 0), 2); cv2.putText(image, label, (box[0], box[1] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)
    return image

# --- 3. 核心处理函数 (已更新视频处理逻辑) ---
def process_single_route(route_path, model_path, process_position):
    pid = os.getpid(); route_name = os.path.basename(route_path)
    print(f"--- [进程 {pid} @ 行 {process_position+1}] 开始处理航线: {route_name} ---")
    device = "cuda:0" if torch.cuda.is_available() else "cpu"
    try:
        model = YOLO(model_path)
        # SAHI模型现在对图片和视频都可能使用
        sahi_model = AutoDetectionModel.from_pretrained(model_type='yolov8', model=model, device=device)
    except Exception as e: print(f"  [进程 {pid}] 致命错误: 模型加载失败: {e}"); return None

    files = os.listdir(route_path)
    image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
    video_files = [f for f in files if f.lower().endswith(('.mp4', '.mov', '.avi'))]
    image_results, video_results = [], []

    # Part A: Process Images (逻辑不变)
    if image_files:
        print(f"  [进程 {pid}] 正在处理 {len(image_files)} 张图片 (使用 SAHI)...")
        # ... (此处省略未改变的图片处理代码) ...
        os.makedirs(os.path.join(ORIGINAL_IMG_DIR, route_name), exist_ok=True); os.makedirs(os.path.join(ANNOTATED_IMG_DIR, route_name), exist_ok=True)
        for img_filename in tqdm(image_files, desc=f"  -> Images", position=process_position + 1, leave=False):
            img_path = os.path.join(route_path, img_filename); image = cv2.imread(img_path)
            if image is None: continue
            detections_on_image = []; img_height, img_width, _ = image.shape; drone_geodata = get_image_geodata(img_path); drone_agl = drone_geodata.get('alt') if drone_geodata else None
            results = get_sliced_prediction(image=image, detection_model=sahi_model, slice_height=SLICE_HEIGHT, slice_width=SLICE_WIDTH, overlap_height_ratio=OVERLAP_RATIO, overlap_width_ratio=OVERLAP_RATIO, verbose=0)
            for pred in results.object_prediction_list:
                bbox_int = [int(c) for c in pred.bbox.to_xyxy()]; target_geo = calculate_target_gps(drone_geodata, drone_agl, img_width, img_height, bbox_int)
                det_data = {'Route': route_name, 'Source File': img_filename, 'class_name': pred.category.name, 'confidence': f"{pred.score.value:.2f}", 'bbox_xyxy': bbox_int, 'target_lat': f"{target_geo['lat']:.6f}" if target_geo else "N/A", 'target_lon': f"{target_geo['lon']:.6f}" if target_geo else "N/A"}
                image_results.append(det_data); detections_on_image.append(det_data)
            if detections_on_image:
                shutil.copy(img_path, os.path.join(ORIGINAL_IMG_DIR, route_name, img_filename))
                annotated_image = draw_detections(image, detections_on_image)
                cv2.imwrite(os.path.join(ANNOTATED_IMG_DIR, route_name, f"annotated_{img_filename}"), annotated_image)

    # Part B: Process Videos (逻辑已更新)
    if video_files:
        for video_filename in video_files:
            video_path = os.path.join(route_path, video_filename)
            os.makedirs(os.path.join(ORIGINAL_KF_DIR, route_name, os.path.splitext(video_filename)[0]), exist_ok=True)
            os.makedirs(os.path.join(ANNOTATED_KF_DIR, route_name, os.path.splitext(video_filename)[0]), exist_ok=True)
            
            keyframes_exported, total_detections, class_counts = 0, 0, defaultdict(int)
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened(): continue

            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_skip = round(fps / VIDEO_FPS_TO_PROCESS) if VIDEO_FPS_TO_PROCESS > 0 and fps > 0 else 1
            frame_idx = 0
            
            desc_text = f"  -> (SAHI) {video_filename[:15]}.." if USE_SAHI_FOR_VIDEOS else f"  -> (Track) {video_filename[:15]}.."
            with tqdm(total=total_frames, desc=desc_text, position=process_position + 1, unit='frame', leave=False) as pbar:
                while True:
                    ret, frame = cap.read()
                    if not ret: break
                    
                    if frame_idx % frame_skip == 0:
                        detections_in_frame = []
                        # ==================== 主要修改点：视频处理分支 ====================
                        if USE_SAHI_FOR_VIDEOS:
                            # --- 使用 SAHI 进行精细检测 ---
                            results = get_sliced_prediction(image=frame, detection_model=sahi_model, slice_height=SLICE_HEIGHT, slice_width=SLICE_WIDTH, overlap_height_ratio=OVERLAP_RATIO, overlap_width_ratio=OVERLAP_RATIO, verbose=0)
                            if results.object_prediction_list:
                                for pred in results.object_prediction_list:
                                    class_name = pred.category.name
                                    class_counts[class_name] += 1
                                    total_detections += 1
                                    detections_in_frame.append({'bbox_xyxy': [int(c) for c in pred.bbox.to_xyxy()], 'class_name': class_name, 'confidence': f"{pred.score.value:.2f}"})
                        else:
                            # --- 使用原有的追踪模式 ---
                            results = model.track(frame, persist=True, tracker=TRACKER_CONFIG, verbose=False)
                            if results and results[0].boxes.id is not None:
                                for box in results[0].boxes:
                                    class_name = model.names[int(box.cls)]
                                    class_counts[class_name] += 1
                                    total_detections += 1
                                    detections_in_frame.append({'bbox_xyxy': [int(c) for c in box.xyxy[0].tolist()], 'class_name': class_name, 'confidence': f"{float(box.conf):.2f}"})
                        # ===============================================================
                        
                        # 如果当前帧有任何检测结果，则保存图片
                        if detections_in_frame:
                            keyframes_exported += 1
                            keyframe_basename = f"{os.path.splitext(video_filename)[0]}_frame_{frame_idx}.jpg"
                            cv2.imwrite(os.path.join(ORIGINAL_KF_DIR, route_name, os.path.splitext(video_filename)[0], keyframe_basename), frame)
                            annotated_frame = draw_detections(frame.copy(), detections_in_frame)
                            cv2.imwrite(os.path.join(ANNOTATED_KF_DIR, route_name, os.path.splitext(video_filename)[0], f"annotated_{keyframe_basename}"), annotated_frame)
                    
                    frame_idx += 1
                    pbar.update(1)
            cap.release()
            
            if keyframes_exported > 0:
                video_results.append({'Route': route_name, 'Video File': video_filename, 'Keyframes Exported': keyframes_exported, 'Total Detections': total_detections, 'Class Counts': dict(class_counts)})

    return {'image_results': image_results, 'video_results': video_results}

# --- 4. 主程序入口 (已更新报告生成逻辑) ---
def main():
    print("INFO: Main function started.")
    for dir_path in [REPORTS_DIR, ORIGINAL_IMG_DIR, ANNOTATED_IMG_DIR, ORIGINAL_KF_DIR, ANNOTATED_KF_DIR]:
        os.makedirs(dir_path, exist_ok=True)
    print(f"INFO: All output directories ensured under '{BASE_OUTPUT_DIR}/'")

    if not os.path.isdir(BASE_DATA_DIR): print(f"FATAL: Base data directory not found: {BASE_DATA_DIR}"); return
    all_routes = [os.path.join(BASE_DATA_DIR, d) for d in sorted(os.listdir(BASE_DATA_DIR)) if os.path.isdir(os.path.join(BASE_DATA_DIR, d))]
    if not all_routes: print("WARNING: No route subdirectories found."); return
    
    routes_to_process = all_routes
    optimal_process_count = 8; num_processes = min(len(routes_to_process), optimal_process_count)
    if num_processes < 1: num_processes = 1
    
    print(f"INFO: Total routes found: {len(all_routes)}. Processing ALL {len(routes_to_process)} routes using {num_processes} processes.")
    print("INFO: Starting detection run...")

    tasks = [(route_path, MODEL_PATH, i) for i, route_path in enumerate(routes_to_process)]
    start_time = time.time()
    
    print("\n" * (num_processes))
    with Pool(processes=num_processes) as pool: results_from_all_processes = pool.starmap(process_single_route, tasks)
    end_time = time.time()
    
    print("\n" * (num_processes + 1)); print(f"INFO: All tasks completed in {end_time - start_time:.2f} seconds."); print("INFO: Aggregating results and generating reports...")

    final_image_results = [item for res in results_from_all_processes if res for item in res['image_results']]
    final_video_results = [item for res in results_from_all_processes if res for item in res['video_results']]

    if final_image_results:
        df_img = pd.DataFrame(final_image_results)
        df_img.to_csv(os.path.join(REPORTS_DIR, "image_detections_report.csv"), index=False, encoding='utf-8-sig')
        print(f"SUCCESS: Image detections report generated.")
    else: print("INFO: No objects detected in any images.")
        
    if final_video_results:
        all_class_names = set(k for item in final_video_results for k in item['Class Counts'].keys())
        flat_data = []
        for item in final_video_results:
            row = {
                'Route': item['Route'],
                'Video File': item['Video File'],
                'Keyframes Exported': item['Keyframes Exported'],
                'Total Detections': item.get('Total Detections', 'N/A') # 使用 get 以兼容旧格式
            }
            for class_name in all_class_names:
                row[f'count_{class_name}'] = item['Class Counts'].get(class_name, 0)
            flat_data.append(row)
        df_vid = pd.DataFrame(flat_data)
        df_vid.to_csv(os.path.join(REPORTS_DIR, "video_statistics_report.csv"), index=False, encoding='utf-8-sig')
        print(f"SUCCESS: Video statistics report generated.")
    else: print("INFO: No objects detected in any videos.")

if __name__ == '__main__':
    main()