#!/usr/bin/env python3
"""
江豚检测系统 - 快速启动脚本
直接启动Web应用
"""

import os
import sys
import time
from pathlib import Path

def check_conda_environment():
    """检查是否在正确的conda环境中"""
    try:
        import os
        conda_env = os.environ.get('CONDA_DEFAULT_ENV', '')
        if conda_env != 'yolov11':
            print(f"⚠️ 当前环境: {conda_env if conda_env else '未知'}")
            print("🔧 推荐使用 'yolov11' 环境")
            print("💡 请运行: conda activate yolov11")
            print("📁 或者双击 start_conda.bat 文件")
            print("-" * 50)
        else:
            print(f"✅ 当前环境: {conda_env}")
    except Exception as e:
        print(f"⚠️ 环境检查失败: {e}")

def main():
    """主函数"""
    print("\n" + "="*50)
    print("🐋 江豚检测系统 - 快速启动")
    print("="*50)
    
    # 检查conda环境
    check_conda_environment()
    
    # 确保src目录在Python路径中
    src_path = Path("src")
    if src_path.exists() and str(src_path.absolute()) not in sys.path:
        sys.path.insert(0, str(src_path.absolute()))
    
    # 检查模型文件
    model_path = Path("best.pt")
    if not model_path.exists():
        print("⚠️ 模型文件不存在: best.pt")
        print("请确保模型文件放置在正确位置")
        return
    
    print("✅ 模型文件存在")
    print("✅ 正在启动标准Web应用...")
    print("🌐 Web应用将在 http://localhost:5000 上运行")
    print("📱 请在浏览器中打开上述地址")
    print("⚡ 按 Ctrl+C 停止应用")
    print("\n" + "="*50)
    
    # 启动Web应用
    try:
        import web_app
        web_app.app.run(host='0.0.0.0', port=5000, debug=False)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    main() 