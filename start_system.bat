@echo off
chcp 65001 >nul
title Finless Porpoise Detection System - Quick Start
color 0A

echo ================================================
echo      Finless Porpoise Detection System
echo ================================================
echo.

:: Activate conda environment
echo Activating conda environment: yolov11
call conda activate yolov11
if %errorlevel% neq 0 (
    echo [ERROR] Failed to activate conda environment 'yolov11'.
    echo Please make sure the environment exists and conda is installed.
    echo Press any key to exit...
    pause >nul
    exit /b
)

:: Check if Python is available in the environment
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python not available in yolov11 environment.
    echo Press any key to exit...
    pause >nul
    exit /b
)

:: Check if run.py exists
if not exist run.py (
    echo [ERROR] run.py not found. Please run this script in the correct directory.
    echo Press any key to exit...
    pause >nul
    exit /b
)

:: Provide startup options
echo Please select startup method:
echo 1. Full startup process (Recommended)
echo 2. Direct web app startup
echo.
set /p choice="Please enter your choice (1/2): "

if "%choice%"=="1" (
    echo.
    echo Starting full process...
    python run.py
) else if "%choice%"=="2" (
    echo.
    echo Starting web app directly...
    python quick_start.py
) else (
    echo.
    echo Invalid choice, starting default process...
    python run.py
)

echo.
echo Press any key to exit...
pause >nul 