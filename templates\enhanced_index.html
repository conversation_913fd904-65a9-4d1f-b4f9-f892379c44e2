<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐋 江豚检测系统 </title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        
        /* 海洋主题动画 */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        @keyframes wave {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(5deg); }
            75% { transform: rotate(-5deg); }
        }
        
        @keyframes bubble {
            0% { transform: translateY(0px) scale(1); opacity: 0.7; }
            50% { transform: translateY(-20px) scale(1.1); opacity: 1; }
            100% { transform: translateY(-40px) scale(1); opacity: 0; }
        }
        
        .porpoise-swim {
            animation: float 3s ease-in-out infinite;
        }
        
        .bubble {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 40%, rgba(255,255,255,0.8), rgba(56,189,248,0.4));
            animation: bubble 4s infinite ease-in-out;
        }
        
        .ocean-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(14, 165, 233, 0.1);
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #0ea5e9, #38bdf8, #0ea5e9);
            background-size: 200% 200%;
            animation: progress-flow 2s ease-in-out infinite;
        }
        
        @keyframes progress-flow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        /* 拖拽区域样式 */
        .dropzone {
            border: 2px dashed #cbd5e1;
            transition: all 0.3s ease;
        }
        
        .dropzone.dragover {
            border-color: #0ea5e9;
            background-color: rgba(14, 165, 233, 0.05);
        }
        
        /* 自定义滚动条 */
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: #f1f5f9; }
        ::-webkit-scrollbar-thumb { background: #64748b; border-radius: 4px; }
        ::-webkit-scrollbar-thumb:hover { background: #475569; }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-slate-100 min-h-screen">
    <!-- 背景气泡效果 -->
    <div class="fixed inset-0 pointer-events-none overflow-hidden">
        <div class="bubble w-4 h-4 opacity-30" style="left: 10%; bottom: 10%; animation-delay: 0s;"></div>
        <div class="bubble w-3 h-3 opacity-40" style="left: 20%; bottom: 15%; animation-delay: 0.5s;"></div>
        <div class="bubble w-5 h-5 opacity-25" style="left: 35%; bottom: 5%; animation-delay: 1s;"></div>
        <div class="bubble w-4 h-4 opacity-35" style="left: 50%; bottom: 20%; animation-delay: 1.5s;"></div>
        <div class="bubble w-3 h-3 opacity-30" style="left: 70%; bottom: 8%; animation-delay: 2s;"></div>
        <div class="bubble w-6 h-6 opacity-20" style="left: 85%; bottom: 12%; animation-delay: 2.5s;"></div>
    </div>

    <!-- 主容器 -->
    <div class="relative z-10 container mx-auto px-4 py-8">
        <!-- 标题区域 -->
        <div class="text-center mb-12">
            <div class="flex items-center justify-center mb-6">
                <!-- 江豚插画 -->
                <div class="w-20 h-20 mr-4 porpoise-swim">
                    <svg viewBox="0 0 200 200" class="w-full h-full">
                        <ellipse cx="100" cy="120" rx="60" ry="35" fill="#64748b" class="drop-shadow-lg"/>
                        <circle cx="100" cy="80" r="35" fill="#64748b" class="drop-shadow-lg"/>
                        <circle cx="88" cy="70" r="8" fill="#1e293b"/>
                        <circle cx="112" cy="70" r="8" fill="#1e293b"/>
                        <circle cx="90" cy="68" r="3" fill="white"/>
                        <circle cx="114" cy="68" r="3" fill="white"/>
                        <path d="M 85 88 Q 100 95 115 88" stroke="#1e293b" stroke-width="3" fill="none" stroke-linecap="round"/>
                        <ellipse cx="70" cy="110" rx="12" ry="20" fill="#475569" transform="rotate(-30 70 110)"/>
                        <ellipse cx="130" cy="110" rx="12" ry="20" fill="#475569" transform="rotate(30 130 110)"/>
                        <path d="M 160 120 Q 170 110 175 115 Q 170 125 160 120 Z" fill="#475569"/>
                        <path d="M 160 120 Q 170 130 175 125 Q 170 115 160 120 Z" fill="#475569"/>
                    </svg>
                </div>
                <div>
                    <h1 class="text-4xl font-bold text-slate-800 mb-2">江豚检测系统</h1>
                    <p class="text-blue-600 text-lg">基于AI技术的海洋生物保护助手</p>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
            <!-- 左侧：上传和设置 -->
            <div class="xl:col-span-1 space-y-6">
                <!-- 文件上传区域 -->
                <div class="ocean-card rounded-2xl p-6">
                    <h2 class="text-xl font-semibold text-slate-800 mb-4 flex items-center">
                        📤 上传图片
                    </h2>
                    
                    <!-- 拖拽上传区域 -->
                    <div id="dropzone" class="dropzone rounded-xl p-8 text-center">
                        <div class="mb-4">
                            <svg class="mx-auto h-12 w-12 text-slate-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <p class="text-slate-600 mb-2">拖拽图片到这里，或者</p>
                        <input type="file" id="fileInput" multiple accept="image/*" class="hidden">
                        <button id="selectBtn" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                            选择文件
                        </button>
                    </div>
                    
                    <!-- 文件列表 -->
                    <div id="fileList" class="mt-4 hidden">
                        <h3 class="font-medium text-slate-700 mb-2">已选择的文件:</h3>
                        <div id="fileItems" class="space-y-2 max-h-32 overflow-y-auto"></div>
                    </div>
                </div>

                <!-- 检测设置 -->
                <div class="ocean-card rounded-2xl p-6">
                    <h2 class="text-xl font-semibold text-slate-800 mb-4 flex items-center">
                        ⚙️ 检测设置
                    </h2>
                    
                    <!-- 置信度设置 -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-slate-700 mb-2">
                            置信度阈值: <span id="confidenceValue" class="text-blue-600 font-bold">0.25</span>
                        </label>
                        <input type="range" id="confidenceSlider" min="0.1" max="0.9" step="0.05" value="0.25" 
                               class="w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer">
                    </div>
                    
                    <!-- SAHI切片检测 -->
                    <div class="mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" id="useSahi" class="rounded text-blue-600 mr-2">
                            <span class="text-sm text-slate-700">启用SAHI切片检测</span>
                        </label>
                        <p class="text-xs text-slate-500 mt-1">适用于高分辨率图片中的小目标检测</p>
                    </div>
                </div>

                <!-- 开始检测按钮 -->
                <button id="startDetection" disabled 
                        class="w-full py-4 px-6 rounded-2xl font-semibold text-white transition-all duration-300 bg-slate-300 cursor-not-allowed">
                    <span class="flex items-center justify-center">
                        ⚡ 开始检测
                    </span>
                </button>
            </div>

            <!-- 右侧：进度和结果 -->
            <div class="xl:col-span-2 space-y-6">
                <!-- 系统状态 -->
                <div class="ocean-card rounded-2xl p-6">
                    <h2 class="text-xl font-semibold text-slate-800 mb-4 flex items-center">
                        📊 系统状态
                    </h2>
                    <div id="systemStatus" class="space-y-2">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-slate-600">连接状态:</span>
                            <span id="connectionStatus" class="text-sm text-slate-500">检查中...</span>
                        </div>
                    </div>
                </div>

                <!-- 进度追踪器 -->
                <div id="progressSection" class="ocean-card rounded-2xl p-6 hidden">
                    <h2 class="text-xl font-semibold text-slate-800 mb-4 flex items-center">
                        👁️ 检测进度
                    </h2>
                    
                    <!-- 进度条 -->
                    <div class="mb-4">
                        <div class="flex justify-between text-sm text-slate-600 mb-2">
                            <span id="progressText">等待开始...</span>
                            <span id="progressPercent">0%</span>
                        </div>
                        <div class="w-full bg-slate-200 rounded-full h-3">
                            <div id="progressBar" class="progress-bar h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <!-- 当前处理文件 -->
                    <div id="currentFile" class="text-sm text-slate-600 bg-slate-50 rounded-lg p-3 hidden">
                        <span class="font-medium">当前处理:</span> <span id="currentFileName"></span>
                    </div>
                    
                    <!-- 检测步骤 -->
                    <div id="detectionSteps" class="mt-4 space-y-2">
                        <div class="flex items-center text-sm">
                            <div id="step1" class="w-4 h-4 rounded-full bg-slate-300 mr-3"></div>
                            <span>读取图片和GPS信息</span>
                        </div>
                        <div class="flex items-center text-sm">
                            <div id="step2" class="w-4 h-4 rounded-full bg-slate-300 mr-3"></div>
                            <span>AI模型检测</span>
                        </div>
                        <div class="flex items-center text-sm">
                            <div id="step3" class="w-4 h-4 rounded-full bg-slate-300 mr-3"></div>
                            <span>生成检测结果</span>
                        </div>
                    </div>
                </div>

                <!-- 检测结果 -->
                <div id="resultsSection" class="ocean-card rounded-2xl p-6 hidden">
                    <h2 class="text-xl font-semibold text-slate-800 mb-4 flex items-center">
                        📋 检测结果
                    </h2>
                    <div id="resultsContent"></div>
                </div>

                <!-- 默认状态：可爱插画 -->
                <div id="welcomeSection" class="ocean-card rounded-2xl p-12 text-center">
                    <div class="w-48 h-48 mx-auto mb-6 porpoise-swim">
                        <svg viewBox="0 0 200 200" class="w-full h-full">
                            <ellipse cx="100" cy="120" rx="60" ry="35" fill="#64748b" class="drop-shadow-lg"/>
                            <circle cx="100" cy="80" r="35" fill="#64748b" class="drop-shadow-lg"/>
                            <circle cx="88" cy="70" r="8" fill="#1e293b"/>
                            <circle cx="112" cy="70" r="8" fill="#1e293b"/>
                            <circle cx="90" cy="68" r="3" fill="white"/>
                            <circle cx="114" cy="68" r="3" fill="white"/>
                            <path d="M 85 88 Q 100 95 115 88" stroke="#1e293b" stroke-width="3" fill="none" stroke-linecap="round"/>
                            <ellipse cx="70" cy="110" rx="12" ry="20" fill="#475569" transform="rotate(-30 70 110)"/>
                            <ellipse cx="130" cy="110" rx="12" ry="20" fill="#475569" transform="rotate(30 130 110)"/>
                            <path d="M 160 120 Q 170 110 175 115 Q 170 125 160 120 Z" fill="#475569"/>
                            <path d="M 160 120 Q 170 130 175 125 Q 170 115 160 120 Z" fill="#475569"/>
                            <!-- 水花效果 -->
                            <circle cx="50" cy="160" r="4" fill="#38bdf8" opacity="0.6">
                                <animate attributeName="r" values="4;8;4" dur="2s" repeatCount="indefinite"/>
                                <animate attributeName="opacity" values="0.6;0.2;0.6" dur="2s" repeatCount="indefinite"/>
                            </circle>
                            <circle cx="150" cy="160" r="3" fill="#38bdf8" opacity="0.5">
                                <animate attributeName="r" values="3;6;3" dur="1.8s" repeatCount="indefinite"/>
                                <animate attributeName="opacity" values="0.5;0.1;0.5" dur="1.8s" repeatCount="indefinite"/>
                            </circle>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-semibold text-slate-700 mb-2">准备就绪！</h3>
                    <p class="text-slate-500">上传您的航拍图片，让AI帮您寻找可爱的江豚朋友们</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let socket;
        let selectedFiles = [];
        let currentTaskId = null;

        // 初始化WebSocket连接
        function initSocket() {
            socket = io();
            
            socket.on('connect', function() {
                console.log('已连接到服务器');
                updateConnectionStatus('已连接', 'text-green-600');
                checkSystemStatus();
            });
            
            socket.on('disconnect', function() {
                console.log('与服务器断开连接');
                updateConnectionStatus('已断开', 'text-red-600');
            });
            
            socket.on('progress_update', function(data) {
                updateProgress(data);
            });
            
            socket.on('task_started', function(data) {
                currentTaskId = data.task_id;
                showProgressSection();
            });
        }

        // 更新连接状态
        function updateConnectionStatus(status, className) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.textContent = status;
            statusElement.className = `text-sm ${className}`;
        }

        // 检查系统状态
        function checkSystemStatus() {
            fetch('/api/system-status')
                .then(response => response.json())
                .then(data => {
                    updateSystemStatusDisplay(data);
                })
                .catch(error => {
                    console.error('系统状态检查失败:', error);
                });
        }

        // 更新系统状态显示
        function updateSystemStatusDisplay(data) {
            const statusContainer = document.getElementById('systemStatus');
            
            const deps = data.dependencies || {};
            const statusItems = [
                { name: 'PyTorch', value: deps.torch || '未安装' },
                { name: 'Ultralytics', value: deps.ultralytics || '未安装' },
                { name: 'SAHI', value: deps.sahi || '未安装' },
                { name: 'OpenCV', value: deps.opencv || '未安装' },
                { name: '模型文件', value: data.model_file ? '存在' : '缺失' },
            ];

            statusContainer.innerHTML = statusItems.map(item => `
                <div class="flex items-center justify-between">
                    <span class="text-sm text-slate-600">${item.name}:</span>
                    <span class="text-sm ${item.value === '未安装' || item.value === '缺失' ? 'text-red-600' : 'text-green-600'}">
                        ${item.value}
                    </span>
                </div>
            `).join('');
        }

        // 文件上传处理
        function initFileUpload() {
            const dropzone = document.getElementById('dropzone');
            const fileInput = document.getElementById('fileInput');
            const selectBtn = document.getElementById('selectBtn');

            // 点击选择文件
            selectBtn.addEventListener('click', () => fileInput.click());

            // 文件选择处理
            fileInput.addEventListener('change', handleFileSelect);

            // 拖拽处理
            dropzone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropzone.classList.add('dragover');
            });

            dropzone.addEventListener('dragleave', () => {
                dropzone.classList.remove('dragover');
            });

            dropzone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropzone.classList.remove('dragover');
                handleFileSelect(e);
            });
        }

        // 处理文件选择
        function handleFileSelect(event) {
            const files = event.target.files || event.dataTransfer.files;
            if (files.length > 0) {
                selectedFiles = Array.from(files);
                updateFileList();
                enableDetectionButton();
            }
        }

        // 更新文件列表显示
        function updateFileList() {
            const fileList = document.getElementById('fileList');
            const fileItems = document.getElementById('fileItems');
            
            if (selectedFiles.length > 0) {
                fileList.classList.remove('hidden');
                fileItems.innerHTML = selectedFiles.map((file, index) => `
                    <div class="flex items-center justify-between bg-slate-50 rounded p-2">
                        <span class="text-sm text-slate-700 truncate">${file.name}</span>
                        <span class="text-xs text-slate-500">${(file.size / 1024 / 1024).toFixed(1)}MB</span>
                    </div>
                `).join('');
            } else {
                fileList.classList.add('hidden');
            }
        }

        // 启用检测按钮
        function enableDetectionButton() {
            const button = document.getElementById('startDetection');
            if (selectedFiles.length > 0) {
                button.disabled = false;
                button.className = 'w-full py-4 px-6 rounded-2xl font-semibold text-white transition-all duration-300 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 transform hover:scale-105';
            } else {
                button.disabled = true;
                button.className = 'w-full py-4 px-6 rounded-2xl font-semibold text-white transition-all duration-300 bg-slate-300 cursor-not-allowed';
            }
        }

        // 设置面板处理
        function initSettings() {
            const confidenceSlider = document.getElementById('confidenceSlider');
            const confidenceValue = document.getElementById('confidenceValue');

            confidenceSlider.addEventListener('input', (e) => {
                confidenceValue.textContent = e.target.value;
            });
        }

        // 开始检测
        function startDetection() {
            if (selectedFiles.length === 0) return;

            // 首先上传文件
            const formData = new FormData();
            selectedFiles.forEach(file => formData.append('files', file));

            fetch('/api/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.files) {
                    // 发送检测任务
                    const settings = {
                        confidence: parseFloat(document.getElementById('confidenceSlider').value),
                        useSahi: document.getElementById('useSahi').checked
                    };

                    socket.emit('start_detection', {
                        files: data.files,
                        settings: settings
                    });
                } else {
                    throw new Error(data.error || '文件上传失败');
                }
            })
            .catch(error => {
                console.error('检测启动失败:', error);
                alert('检测启动失败: ' + error.message);
            });
        }

        // 显示进度区域
        function showProgressSection() {
            document.getElementById('welcomeSection').classList.add('hidden');
            document.getElementById('progressSection').classList.remove('hidden');
        }

        // 更新进度显示
        function updateProgress(data) {
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            const progressPercent = document.getElementById('progressPercent');
            const currentFile = document.getElementById('currentFile');
            const currentFileName = document.getElementById('currentFileName');

            // 更新进度条
            progressBar.style.width = `${data.percentage}%`;
            progressPercent.textContent = `${data.percentage}%`;
            progressText.textContent = data.message;

            // 更新当前文件
            if (data.currentFile) {
                currentFile.classList.remove('hidden');
                currentFileName.textContent = data.currentFile;
            } else {
                currentFile.classList.add('hidden');
            }

            // 更新步骤状态
            updateStepStatus(data);

            // 检测完成
            if (data.completed && data.results) {
                showResults(data.results, data.total_detections);
            }

            // 错误处理
            if (data.error) {
                progressText.className = 'text-red-600 font-medium';
            }
        }

        // 更新步骤状态
        function updateStepStatus(data) {
            const totalSteps = data.totalSteps || 1;
            const currentStep = data.currentStep || 0;
            
            const step1 = document.getElementById('step1');
            const step2 = document.getElementById('step2');
            const step3 = document.getElementById('step3');

            // 重置所有步骤
            [step1, step2, step3].forEach(step => {
                step.className = 'w-4 h-4 rounded-full bg-slate-300 mr-3';
            });

            // 根据进度更新步骤状态
            const progress = currentStep / totalSteps;
            if (progress > 0.25) step1.className = 'w-4 h-4 rounded-full bg-blue-500 mr-3';
            if (progress > 0.50) step2.className = 'w-4 h-4 rounded-full bg-blue-500 mr-3';
            if (progress > 0.75) step3.className = 'w-4 h-4 rounded-full bg-blue-500 mr-3';
        }

        // 显示检测结果
        function showResults(results, totalDetections) {
            const resultsSection = document.getElementById('resultsSection');
            const resultsContent = document.getElementById('resultsContent');
            
            resultsSection.classList.remove('hidden');
            
            if (totalDetections > 0) {
                resultsContent.innerHTML = `
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                        <h3 class="text-lg font-semibold text-green-800 mb-2">🎉 检测成功！</h3>
                        <p class="text-green-700">共发现 <strong>${totalDetections}</strong> 个江豚目标</p>
                    </div>
                    
                    <div class="space-y-3">
                        ${results.slice(0, 5).map(result => `
                            <div class="bg-slate-50 rounded-lg p-3">
                                <div class="flex justify-between items-start mb-2">
                                    <span class="font-medium text-slate-800">${result.filename}</span>
                                    <span class="text-sm text-blue-600 font-medium">${(result.confidence * 100).toFixed(1)}%</span>
                                </div>
                                <div class="text-sm text-slate-600">
                                    ${result.latitude && result.longitude ? 
                                        `📍 GPS: ${result.latitude}, ${result.longitude}` : 
                                        '📍 GPS: 无位置信息'
                                    }
                                </div>
                            </div>
                        `).join('')}
                        
                        ${results.length > 5 ? 
                            `<div class="text-center text-slate-500 text-sm">还有 ${results.length - 5} 个检测结果...</div>` : 
                            ''
                        }
                    </div>
                    
                    <div class="mt-6 text-center">
                        <button onclick="downloadResults()" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                            📥 下载详细报告
                        </button>
                    </div>
                `;
            } else {
                resultsContent.innerHTML = `
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                        <h3 class="text-lg font-semibold text-yellow-800 mb-2">😔 未发现目标</h3>
                        <p class="text-yellow-700">在当前图片中未检测到江豚，请尝试调整检测设置或上传其他图片</p>
                    </div>
                `;
            }
        }

        // 下载结果
        function downloadResults() {
            if (currentTaskId) {
                window.open(`/api/download-results/${currentTaskId}`, '_blank');
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initSocket();
            initFileUpload();
            initSettings();
            
            // 绑定开始检测按钮
            document.getElementById('startDetection').addEventListener('click', startDetection);
            
            console.log('🐋 江豚检测系统已启动');
        });
    </script>
</body>
</html> 